"""
Tests for Settings Management

This module contains tests for the settings system.
"""

import unittest
import tempfile
import json
from pathlib import Path

from config.settings import Settings

class TestSettings(unittest.TestCase):
    """Test cases for Settings class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test settings
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_file = Path(self.temp_dir) / "test_settings.json"
        
        # Create settings instance with test config file
        self.settings = Settings(str(self.test_config_file))
        
    def tearDown(self):
        """Clean up test environment."""
        # Clean up temporary files
        if self.test_config_file.exists():
            self.test_config_file.unlink()
        Path(self.temp_dir).rmdir()
        
    def test_default_settings(self):
        """Test that default settings are loaded correctly."""
        # Check that default values are present
        self.assertEqual(self.settings.get('app.version'), '1.0.0')
        self.assertTrue(self.settings.get('app.auto_save'))
        self.assertEqual(self.settings.get('email.port'), 993)
        
    def test_get_setting(self):
        """Test getting settings with dot notation."""
        # Test existing setting
        version = self.settings.get('app.version')
        self.assertEqual(version, '1.0.0')
        
        # Test non-existing setting with default
        non_existing = self.settings.get('non.existing.setting', 'default_value')
        self.assertEqual(non_existing, 'default_value')
        
    def test_set_setting(self):
        """Test setting values with dot notation."""
        # Set a new value
        self.settings.set('test.setting', 'test_value')
        
        # Verify it was set
        self.assertEqual(self.settings.get('test.setting'), 'test_value')
        
    def test_save_and_load(self):
        """Test saving and loading settings."""
        # Set some test values
        self.settings.set('test.value1', 'hello')
        self.settings.set('test.value2', 42)
        
        # Save settings
        self.settings.save()
        
        # Create new settings instance with same file
        new_settings = Settings(str(self.test_config_file))
        
        # Verify values were loaded
        self.assertEqual(new_settings.get('test.value1'), 'hello')
        self.assertEqual(new_settings.get('test.value2'), 42)
        
    def test_reset_to_defaults(self):
        """Test resetting settings to defaults."""
        # Modify a non-default setting
        self.settings.set('test.custom_setting', 'custom_value')
        self.assertEqual(self.settings.get('test.custom_setting'), 'custom_value')

        # Reset to defaults
        self.settings.reset_to_defaults()

        # Verify custom setting was removed and defaults are restored
        self.assertIsNone(self.settings.get('test.custom_setting'))
        self.assertEqual(self.settings.get('app.version'), '1.0.0')
        
    def test_create_directories(self):
        """Test directory creation."""
        # Set test directories
        test_dir1 = Path(self.temp_dir) / "test_dir1"
        test_dir2 = Path(self.temp_dir) / "test_dir2"
        
        self.settings.set('email.download_folder', str(test_dir1))
        self.settings.set('files.temp_folder', str(test_dir2))
        
        # Create directories
        self.settings.create_directories()
        
        # Verify directories were created
        self.assertTrue(test_dir1.exists())
        self.assertTrue(test_dir2.exists())
        
        # Clean up
        test_dir1.rmdir()
        test_dir2.rmdir()

if __name__ == '__main__':
    unittest.main()
