"""
Task Manager for Desktop Assistant

This module handles task coordination and workflow management.
"""

import logging
import threading
import time
from typing import Dict, List, Callable, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from utils.error_handler import handle_exceptions, DesktopAssistantError

class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Task:
    """Task definition."""
    id: str
    name: str
    function: Callable
    args: tuple = ()
    kwargs: dict = None
    dependencies: List[str] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.dependencies is None:
            self.dependencies = []

class TaskManager:
    """Manages task execution and workflow coordination."""
    
    def __init__(self, progress_callback: Optional[Callable[[str, float], None]] = None):
        """Initialize task manager.
        
        Args:
            progress_callback: Callback for progress updates (task_name, progress)
        """
        self.logger = logging.getLogger(__name__)
        self.tasks: Dict[str, Task] = {}
        self.progress_callback = progress_callback
        self.is_running = False
        self.current_task = None
        self._stop_event = threading.Event()
        
    def add_task(
        self,
        task_id: str,
        name: str,
        function: Callable,
        args: tuple = (),
        kwargs: dict = None,
        dependencies: List[str] = None
    ) -> Task:
        """Add a task to the manager.
        
        Args:
            task_id: Unique task identifier
            name: Human-readable task name
            function: Function to execute
            args: Function arguments
            kwargs: Function keyword arguments
            dependencies: List of task IDs that must complete first
            
        Returns:
            Created task object
        """
        task = Task(
            id=task_id,
            name=name,
            function=function,
            args=args,
            kwargs=kwargs or {},
            dependencies=dependencies or []
        )
        
        self.tasks[task_id] = task
        self.logger.info(f"Added task: {name} ({task_id})")
        return task
        
    def remove_task(self, task_id: str) -> bool:
        """Remove a task from the manager.
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if task was removed, False if not found
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.RUNNING:
                self.logger.warning(f"Cannot remove running task: {task_id}")
                return False
            del self.tasks[task_id]
            self.logger.info(f"Removed task: {task_id}")
            return True
        return False
        
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task object or None if not found
        """
        return self.tasks.get(task_id)
        
    def get_ready_tasks(self) -> List[Task]:
        """Get tasks that are ready to run (dependencies satisfied).
        
        Returns:
            List of ready tasks
        """
        ready_tasks = []
        
        for task in self.tasks.values():
            if task.status != TaskStatus.PENDING:
                continue
                
            # Check if all dependencies are completed
            dependencies_met = True
            for dep_id in task.dependencies:
                dep_task = self.tasks.get(dep_id)
                if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                    dependencies_met = False
                    break
                    
            if dependencies_met:
                ready_tasks.append(task)
                
        return ready_tasks
        
    @handle_exceptions(context="task execution")
    def execute_task(self, task: Task) -> bool:
        """Execute a single task.
        
        Args:
            task: Task to execute
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Starting task: {task.name}")
            task.status = TaskStatus.RUNNING
            task.start_time = datetime.now()
            self.current_task = task
            
            # Update progress
            if self.progress_callback:
                self.progress_callback(task.name, 0.0)
                
            # Execute the task function
            if hasattr(task.function, '__self__'):
                # Method call - add progress callback if supported
                if 'progress_callback' in task.function.__code__.co_varnames:
                    task.kwargs['progress_callback'] = lambda p: self._update_task_progress(task, p)
                    
            task.result = task.function(*task.args, **task.kwargs)
            
            # Mark as completed
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()
            task.progress = 100.0
            
            if self.progress_callback:
                self.progress_callback(task.name, 100.0)
                
            duration = (task.end_time - task.start_time).total_seconds()
            self.logger.info(f"Completed task: {task.name} in {duration:.2f}s")
            return True
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = e
            task.end_time = datetime.now()
            
            self.logger.error(f"Task failed: {task.name} - {e}")
            return False
            
        finally:
            self.current_task = None
            
    def _update_task_progress(self, task: Task, progress: float):
        """Update task progress.
        
        Args:
            task: Task being updated
            progress: Progress value (0-100)
        """
        task.progress = progress
        if self.progress_callback:
            self.progress_callback(task.name, progress)
            
    def execute_all(self, max_concurrent: int = 1) -> bool:
        """Execute all tasks in dependency order.
        
        Args:
            max_concurrent: Maximum number of concurrent tasks
            
        Returns:
            True if all tasks completed successfully
        """
        if self.is_running:
            self.logger.warning("Task manager is already running")
            return False
            
        self.is_running = True
        self._stop_event.clear()
        
        try:
            self.logger.info(f"Starting execution of {len(self.tasks)} tasks")
            
            while True:
                # Check for stop signal
                if self._stop_event.is_set():
                    self.logger.info("Task execution stopped by user")
                    break
                    
                # Get ready tasks
                ready_tasks = self.get_ready_tasks()
                
                if not ready_tasks:
                    # Check if we're done or stuck
                    pending_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.PENDING]
                    if not pending_tasks:
                        break  # All done
                    else:
                        self.logger.error("No ready tasks but pending tasks exist - dependency cycle?")
                        return False
                        
                # Execute ready tasks (for now, just one at a time)
                for task in ready_tasks[:max_concurrent]:
                    if self._stop_event.is_set():
                        break
                    self.execute_task(task)
                    
                # Small delay to prevent busy waiting
                time.sleep(0.1)
                
            # Check results
            failed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.FAILED]
            if failed_tasks:
                self.logger.error(f"{len(failed_tasks)} tasks failed")
                return False
                
            completed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]
            self.logger.info(f"Successfully completed {len(completed_tasks)} tasks")
            return True
            
        finally:
            self.is_running = False
            
    def stop(self):
        """Stop task execution."""
        self.logger.info("Stopping task execution...")
        self._stop_event.set()
        
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending task.
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if cancelled, False if not found or already running
        """
        task = self.tasks.get(task_id)
        if not task:
            return False
            
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            self.logger.info(f"Cancelled task: {task.name}")
            return True
            
        return False
        
    def clear_completed(self):
        """Clear completed and failed tasks."""
        to_remove = []
        for task_id, task in self.tasks.items():
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                to_remove.append(task_id)
                
        for task_id in to_remove:
            del self.tasks[task_id]
            
        self.logger.info(f"Cleared {len(to_remove)} completed tasks")
        
    def get_status_summary(self) -> Dict[str, int]:
        """Get summary of task statuses.
        
        Returns:
            Dictionary with status counts
        """
        summary = {status.value: 0 for status in TaskStatus}
        
        for task in self.tasks.values():
            summary[task.status.value] += 1
            
        return summary
        
    def get_overall_progress(self) -> float:
        """Get overall progress percentage.
        
        Returns:
            Progress percentage (0-100)
        """
        if not self.tasks:
            return 100.0
            
        total_progress = sum(task.progress for task in self.tasks.values())
        return total_progress / len(self.tasks)
