"""
Tests for File Manager

This module contains tests for the file management functionality.
"""

import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch, mock_open
from pathlib import Path

from modules.file_manager import FileManager
from config.settings import Settings
from utils.error_handler import FileError

class TestFileManager(unittest.TestCase):
    """Test cases for FileManager class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_file = Path(self.temp_dir) / "test_settings.json"
        
        # Create settings instance with test config file
        self.settings = Settings(str(self.test_config_file))
        
        # Configure test file settings
        self.settings.set('files.temp_folder', str(Path(self.temp_dir) / "temp"))
        self.settings.set('files.processed_folder', str(Path(self.temp_dir) / "processed"))
        self.settings.set('files.backup_folder', str(Path(self.temp_dir) / "backup"))
        self.settings.set('files.max_file_size_mb', 10)
        
        # Create file manager
        self.file_manager = FileManager(self.settings)
        
        # Create test files
        self.test_file = Path(self.temp_dir) / "test_video.mp4"
        self.test_file.write_text("fake video content")
        
        self.test_file2 = Path(self.temp_dir) / "test_image.jpg"
        self.test_file2.write_text("fake image content")
        
    def tearDown(self):
        """Clean up test environment."""
        # Clean up temporary files
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_initialization(self):
        """Test file manager initialization."""
        self.assertIsNotNone(self.file_manager.settings)
        self.assertIsNotNone(self.file_manager.logger)
        self.assertIsInstance(self.file_manager.video_extensions, set)
        
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Test invalid characters
        result = self.file_manager._sanitize_filename("test<>file|name?.mp4")
        self.assertNotIn('<', result)
        self.assertNotIn('>', result)
        self.assertNotIn('|', result)
        self.assertNotIn('?', result)
        
        # Test empty filename
        result = self.file_manager._sanitize_filename("")
        self.assertEqual(result, "unnamed_file")
        
        # Test long filename
        long_name = "a" * 250
        result = self.file_manager._sanitize_filename(long_name)
        self.assertLessEqual(len(result), 200)
        
    def test_get_unique_filename(self):
        """Test unique filename generation."""
        # Create a file that already exists
        existing_file = Path(self.temp_dir) / "existing.txt"
        existing_file.write_text("content")
        
        # Test unique filename generation
        unique_path = self.file_manager._get_unique_filename(existing_file)
        self.assertNotEqual(unique_path, existing_file)
        self.assertTrue(str(unique_path).endswith("_1.txt"))
        
    def test_rename_file(self):
        """Test file renaming."""
        original_path = str(self.test_file)
        new_name = "renamed_video.mp4"
        
        # Test rename
        new_path = self.file_manager.rename_file(original_path, new_name)
        
        # Verify rename
        self.assertTrue(Path(new_path).exists())
        self.assertFalse(self.test_file.exists())
        self.assertTrue(new_path.endswith(new_name))
        
    def test_rename_file_with_sanitization(self):
        """Test file renaming with sanitization."""
        original_path = str(self.test_file)
        invalid_name = "bad<>name|?.mp4"
        
        # Test rename with sanitization
        new_path = self.file_manager.rename_file(original_path, invalid_name, sanitize=True)
        
        # Verify sanitization occurred
        new_filename = Path(new_path).name
        self.assertNotIn('<', new_filename)
        self.assertNotIn('>', new_filename)
        
    def test_smart_rename(self):
        """Test smart renaming with pattern."""
        original_path = str(self.test_file)
        title = "Test Video Title"
        
        # Test smart rename
        new_path = self.file_manager.smart_rename(original_path, title)
        
        # Verify smart rename
        self.assertTrue(Path(new_path).exists())
        self.assertIn("Test Video Title", Path(new_path).name)
        
    def test_move_file(self):
        """Test file moving."""
        source_path = str(self.test_file)
        dest_dir = Path(self.temp_dir) / "destination"
        dest_dir.mkdir()
        
        # Test move
        new_path = self.file_manager.move_file(source_path, str(dest_dir))
        
        # Verify move
        self.assertTrue(Path(new_path).exists())
        self.assertFalse(self.test_file.exists())
        self.assertEqual(Path(new_path).parent, dest_dir)
        
    def test_copy_file(self):
        """Test file copying."""
        source_path = str(self.test_file)
        dest_dir = Path(self.temp_dir) / "destination"
        dest_dir.mkdir()
        
        # Test copy
        new_path = self.file_manager.copy_file(source_path, str(dest_dir))
        
        # Verify copy
        self.assertTrue(Path(new_path).exists())
        self.assertTrue(self.test_file.exists())  # Original should still exist
        self.assertEqual(Path(new_path).parent, dest_dir)
        
    def test_delete_file(self):
        """Test file deletion."""
        file_path = str(self.test_file)
        
        # Test delete
        result = self.file_manager.delete_file(file_path, backup=False)
        
        # Verify deletion
        self.assertTrue(result)
        self.assertFalse(self.test_file.exists())
        
    def test_validate_file(self):
        """Test file validation."""
        file_path = str(self.test_file)
        
        # Test validation
        file_info = self.file_manager.validate_file(file_path)
        
        # Verify validation results
        self.assertIn('name', file_info)
        self.assertIn('size_bytes', file_info)
        self.assertIn('extension', file_info)
        self.assertIn('validation', file_info)
        self.assertIn('is_video', file_info)
        
    def test_detect_file_type(self):
        """Test file type detection."""
        # Test video file
        video_info = self.file_manager._detect_file_type(self.test_file)
        self.assertTrue(video_info.get('is_video', False))
        
        # Test image file
        image_info = self.file_manager._detect_file_type(self.test_file2)
        self.assertTrue(image_info.get('is_image', False))
        
    def test_organize_files_by_type(self):
        """Test file organization by type."""
        # Create test files of different types
        video_file = Path(self.temp_dir) / "video.mp4"
        image_file = Path(self.temp_dir) / "image.jpg"
        video_file.write_text("video content")
        image_file.write_text("image content")
        
        # Test organization
        result = self.file_manager.organize_files(self.temp_dir, organize_by="type", create_subfolders=False)
        
        # Verify organization
        self.assertIn('videos', result)
        self.assertIn('images', result)
        self.assertTrue(len(result['videos']) > 0)
        self.assertTrue(len(result['images']) > 0)
        
    def test_batch_rename(self):
        """Test batch renaming."""
        # Create multiple test files
        for i in range(3):
            test_file = Path(self.temp_dir) / f"test_{i}.mp4"
            test_file.write_text(f"content {i}")
            
        # Test batch rename
        pattern = "video_{counter:03d}"
        renamed_files = self.file_manager.batch_rename(self.temp_dir, pattern, file_filter=".mp4")
        
        # Verify batch rename
        self.assertGreater(len(renamed_files), 0)
        for file_path in renamed_files:
            self.assertIn("video_", Path(file_path).name)
            
    def test_find_duplicates(self):
        """Test duplicate file detection."""
        # Create duplicate files
        content = "duplicate content"
        file1 = Path(self.temp_dir) / "file1.txt"
        file2 = Path(self.temp_dir) / "file2.txt"
        file1.write_text(content)
        file2.write_text(content)
        
        # Test duplicate detection
        duplicates = self.file_manager.find_duplicates(self.temp_dir)
        
        # Verify duplicates found
        self.assertGreater(len(duplicates), 0)
        
    def test_get_folder_stats(self):
        """Test folder statistics."""
        # Test folder stats
        stats = self.file_manager.get_folder_stats(self.temp_dir)
        
        # Verify stats
        self.assertIn('total_files', stats)
        self.assertIn('total_size', stats)
        self.assertIn('file_types', stats)
        self.assertGreater(stats['total_files'], 0)
        
    def test_cleanup_empty_folders(self):
        """Test empty folder cleanup."""
        # Create empty folder
        empty_folder = Path(self.temp_dir) / "empty"
        empty_folder.mkdir()
        
        # Test cleanup
        removed_count = self.file_manager.cleanup_empty_folders(self.temp_dir)
        
        # Verify cleanup
        self.assertGreaterEqual(removed_count, 0)
        
    def test_format_file_size(self):
        """Test file size formatting."""
        # Test various sizes
        self.assertEqual(self.file_manager._format_file_size(500), "500.0B")
        self.assertEqual(self.file_manager._format_file_size(1536), "1.5KB")
        self.assertEqual(self.file_manager._format_file_size(1048576), "1.0MB")
        
    def test_calculate_file_hash(self):
        """Test file hash calculation."""
        file_hash = self.file_manager._calculate_file_hash(self.test_file)
        
        # Verify hash
        self.assertIsInstance(file_hash, str)
        self.assertGreater(len(file_hash), 0)
        
    def test_get_file_info(self):
        """Test getting file information."""
        file_info = self.file_manager.get_file_info(str(self.test_file))
        
        # Verify file info
        self.assertIn('name', file_info)
        self.assertIn('size', file_info)
        self.assertIn('extension', file_info)
        self.assertTrue(file_info['is_file'])
        
    def test_create_directory(self):
        """Test directory creation."""
        new_dir = Path(self.temp_dir) / "new_directory"
        
        # Test directory creation
        result = self.file_manager.create_directory(str(new_dir))
        
        # Verify creation
        self.assertTrue(result)
        self.assertTrue(new_dir.exists())
        self.assertTrue(new_dir.is_dir())

if __name__ == '__main__':
    unittest.main()
