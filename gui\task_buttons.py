"""
Task Button Panel for Desktop Assistant

This module contains the task button panel with buttons for various operations.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Callable, Optional

class TaskButtonPanel(ttk.Frame):
    """Panel containing task buttons for the main operations."""
    
    def __init__(self, parent, main_window):
        """Initialize the task button panel.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        super().__init__(parent)
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
        
        self._setup_buttons()
        
    def _setup_buttons(self):
        """Set up all task buttons."""
        # Title for the panel
        title_label = ttk.Label(self, text="Available Tasks", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Email processing button
        self.email_btn = ttk.Button(
            self,
            text="📧 Check Email & Download Videos",
            command=self._handle_email_task,
            width=30
        )
        self.email_btn.pack(pady=5, fill=tk.X)
        
        # File management button
        self.file_btn = ttk.Button(
            self,
            text="📁 Manage Files",
            command=self._handle_file_task,
            width=30
        )
        self.file_btn.pack(pady=5, fill=tk.X)
        
        # DeepSeek integration button
        self.deepseek_btn = ttk.Button(
            self,
            text="🔍 DeepSeek Search & Rename",
            command=self._handle_deepseek_task,
            width=30
        )
        self.deepseek_btn.pack(pady=5, fill=tk.X)
        
        # YouTube upload button
        self.youtube_btn = ttk.Button(
            self,
            text="📺 Upload to YouTube",
            command=self._handle_youtube_task,
            width=30
        )
        self.youtube_btn.pack(pady=5, fill=tk.X)
        
        # Separator
        separator = ttk.Separator(self, orient='horizontal')
        separator.pack(fill=tk.X, pady=10)
        
        # Full workflow button
        self.workflow_btn = ttk.Button(
            self,
            text="🚀 Run Full Workflow",
            command=self._handle_full_workflow,
            width=30,
            style="Accent.TButton"
        )
        self.workflow_btn.pack(pady=5, fill=tk.X)
        
        # Settings button
        self.settings_btn = ttk.Button(
            self,
            text="⚙️ Settings",
            command=self._handle_settings,
            width=30
        )
        self.settings_btn.pack(pady=(20, 5), fill=tk.X)
        
    def _handle_email_task(self):
        """Handle email checking and video download task."""
        self.logger.info("Email task button clicked")
        self.main_window.update_status("Starting email task...")

        # Run email task in task manager
        self._run_email_workflow()
        
    def _handle_file_task(self):
        """Handle file management task."""
        self.logger.info("File management task button clicked")
        self.main_window.update_status("Starting file management...")

        # Run file management workflow
        self._run_file_workflow()
        
    def _handle_deepseek_task(self):
        """Handle DeepSeek search and rename task."""
        self.logger.info("DeepSeek task button clicked")
        self.main_window.update_status("Starting DeepSeek AI workflow...")

        # Run DeepSeek workflow
        self._run_deepseek_workflow()
        
    def _handle_youtube_task(self):
        """Handle YouTube upload task."""
        self.logger.info("YouTube task button clicked")
        self.main_window.update_status("YouTube task will be implemented in Stage 6")
        messagebox.showinfo(
            "YouTube Upload", 
            "YouTube upload functionality will be implemented in Stage 6"
        )
        
    def _handle_full_workflow(self):
        """Handle full workflow execution."""
        self.logger.info("Full workflow button clicked")
        self.main_window.update_status("Full workflow will be implemented in Stage 8")
        messagebox.showinfo(
            "Full Workflow", 
            "Full workflow automation will be implemented in Stage 8"
        )
        
    def _handle_settings(self):
        """Handle settings dialog."""
        self.logger.info("Settings button clicked")
        self.main_window.update_status("Opening settings dialog...")
        self.main_window._open_settings()
        
    def set_button_state(self, button_name: str, state: str):
        """Set the state of a specific button.
        
        Args:
            button_name: Name of the button ('email', 'file', 'deepseek', 'youtube', 'workflow')
            state: Button state ('normal', 'disabled')
        """
        button_map = {
            'email': self.email_btn,
            'file': self.file_btn,
            'deepseek': self.deepseek_btn,
            'youtube': self.youtube_btn,
            'workflow': self.workflow_btn,
            'settings': self.settings_btn
        }
        
        if button_name in button_map:
            button_map[button_name].config(state=state)
        else:
            self.logger.warning(f"Unknown button name: {button_name}")
            
    def disable_all_buttons(self):
        """Disable all task buttons."""
        for button_name in ['email', 'file', 'deepseek', 'youtube', 'workflow']:
            self.set_button_state(button_name, 'disabled')
            
    def enable_all_buttons(self):
        """Enable all task buttons."""
        for button_name in ['email', 'file', 'deepseek', 'youtube', 'workflow']:
            self.set_button_state(button_name, 'normal')

    def _run_email_workflow(self):
        """Run the email checking and download workflow."""
        import threading
        from modules.email_handler import EmailHandler
        from tkinter import simpledialog

        def email_task():
            try:
                # Disable buttons during execution
                self.disable_all_buttons()

                # Get email handler
                email_handler = EmailHandler(self.main_window.settings)

                # Connect to email
                self.main_window.progress_display.log_info("Connecting to email server...")
                if not email_handler.connect():
                    self.main_window.progress_display.log_error("Failed to connect to email server")
                    messagebox.showerror("Email Error", "Failed to connect to email server. Please check your settings.")
                    return

                self.main_window.progress_display.log_success("Connected to email server")

                # Get search parameters from user
                self.main_window.root.after(0, lambda: self._get_search_parameters(email_handler))

            except Exception as e:
                self.logger.error(f"Email workflow failed: {e}")
                self.main_window.progress_display.log_error(f"Email workflow failed: {e}")
                messagebox.showerror("Email Error", f"Email workflow failed: {e}")
            finally:
                self.enable_all_buttons()

        # Run in separate thread
        thread = threading.Thread(target=email_task, daemon=True)
        thread.start()

    def _get_search_parameters(self, email_handler):
        """Get search parameters from user and continue workflow."""
        from tkinter import simpledialog

        try:
            # Get sender email
            sender = simpledialog.askstring(
                "Email Search",
                "Enter sender email address (or leave empty for all):",
                initialvalue=""
            )

            if sender is None:  # User cancelled
                email_handler.disconnect()
                self.enable_all_buttons()
                return

            # Get subject filter
            subject = simpledialog.askstring(
                "Email Search",
                "Enter subject keywords (or leave empty for all):",
                initialvalue=""
            )

            if subject is None:  # User cancelled
                email_handler.disconnect()
                self.enable_all_buttons()
                return

            # Continue with search
            self._continue_email_workflow(email_handler, sender, subject)

        except Exception as e:
            self.logger.error(f"Failed to get search parameters: {e}")
            email_handler.disconnect()
            self.enable_all_buttons()

    def _continue_email_workflow(self, email_handler, sender, subject):
        """Continue email workflow with search and download."""
        import threading

        def search_and_download():
            try:
                # Search for emails with video attachments
                self.main_window.progress_display.log_info(f"Searching for emails from '{sender}' with subject '{subject}'...")

                def search_progress(current, total):
                    progress = (current / total) * 50  # First 50% for search
                    self.main_window.progress_display.update_progress(progress, f"Searching emails ({current}/{total})")

                emails = email_handler.search_emails_with_attachments(
                    sender=sender,
                    subject_contains=subject,
                    limit=10,
                    days_back=30
                )

                if not emails:
                    self.main_window.progress_display.log_warning("No emails with video attachments found")
                    messagebox.showinfo("Email Search", "No emails with video attachments found.")
                    return

                self.main_window.progress_display.log_success(f"Found {len(emails)} emails with video attachments")

                # Download attachments from found emails
                total_downloads = 0
                for i, email_data in enumerate(emails):
                    try:
                        email_id = email_data['id']
                        email_subject = email_data['subject']

                        self.main_window.progress_display.log_info(f"Downloading attachments from: {email_subject}")

                        def download_progress(filename, current, total):
                            overall_progress = 50 + ((i + current/total) / len(emails)) * 50
                            self.main_window.progress_display.update_progress(
                                overall_progress,
                                f"Downloading: {filename}"
                            )

                        downloaded_files = email_handler.download_attachments(
                            email_id,
                            progress_callback=download_progress
                        )

                        total_downloads += len(downloaded_files)

                        for file_path in downloaded_files:
                            self.main_window.progress_display.log_success(f"Downloaded: {file_path}")

                        # Mark email as read
                        email_handler.mark_as_read(email_id)

                    except Exception as e:
                        self.main_window.progress_display.log_error(f"Failed to process email: {e}")
                        continue

                # Complete
                self.main_window.progress_display.update_progress(100, "Email workflow completed")
                self.main_window.progress_display.log_success(f"Email workflow completed! Downloaded {total_downloads} files.")

                messagebox.showinfo(
                    "Email Workflow Complete",
                    f"Successfully downloaded {total_downloads} video files from {len(emails)} emails."
                )

            except Exception as e:
                self.logger.error(f"Email workflow failed: {e}")
                self.main_window.progress_display.log_error(f"Email workflow failed: {e}")
                messagebox.showerror("Email Error", f"Email workflow failed: {e}")
            finally:
                email_handler.disconnect()
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        # Run in separate thread
        thread = threading.Thread(target=search_and_download, daemon=True)
        thread.start()

    def _run_file_workflow(self):
        """Run the file management workflow."""
        import threading
        from modules.file_manager import FileManager
        from tkinter import simpledialog, filedialog

        def file_task():
            try:
                # Disable buttons during execution
                self.disable_all_buttons()

                # Get file manager
                file_manager = FileManager(self.main_window.settings)

                # Get operation choice from user
                self.main_window.root.after(0, lambda: self._get_file_operation(file_manager))

            except Exception as e:
                self.logger.error(f"File workflow failed: {e}")
                self.main_window.progress_display.log_error(f"File workflow failed: {e}")
                messagebox.showerror("File Error", f"File workflow failed: {e}")
            finally:
                self.enable_all_buttons()

        # Run in separate thread
        thread = threading.Thread(target=file_task, daemon=True)
        thread.start()

    def _get_file_operation(self, file_manager):
        """Get file operation choice from user."""
        from tkinter import simpledialog

        try:
            # Show operation menu
            operations = [
                "1. Organize downloaded files",
                "2. Rename files intelligently",
                "3. Validate video files",
                "4. Find duplicate files",
                "5. Clean up empty folders",
                "6. Get folder statistics"
            ]

            choice = simpledialog.askstring(
                "File Management",
                "Choose an operation:\n\n" + "\n".join(operations) + "\n\nEnter number (1-6):",
                initialvalue="1"
            )

            if choice is None:  # User cancelled
                self.enable_all_buttons()
                return

            # Execute chosen operation
            if choice == "1":
                self._organize_files_workflow(file_manager)
            elif choice == "2":
                self._rename_files_workflow(file_manager)
            elif choice == "3":
                self._validate_files_workflow(file_manager)
            elif choice == "4":
                self._find_duplicates_workflow(file_manager)
            elif choice == "5":
                self._cleanup_folders_workflow(file_manager)
            elif choice == "6":
                self._folder_stats_workflow(file_manager)
            else:
                messagebox.showwarning("Invalid Choice", "Please enter a number between 1 and 6")
                self.enable_all_buttons()

        except Exception as e:
            self.logger.error(f"Failed to get file operation: {e}")
            self.enable_all_buttons()

    def _organize_files_workflow(self, file_manager):
        """Run file organization workflow."""
        import threading
        from tkinter import filedialog, simpledialog

        def organize_task():
            try:
                # Get folder to organize
                folder = filedialog.askdirectory(
                    title="Select folder to organize",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Organizing files in: {folder}")
                self.main_window.progress_display.update_progress(25, "Organizing files...")

                # Organize files by type
                result = file_manager.organize_files(folder, organize_by="type", create_subfolders=True)

                self.main_window.progress_display.update_progress(100, "Organization complete")

                # Show results
                summary = []
                for category, files in result.items():
                    if files:
                        summary.append(f"{category.title()}: {len(files)} files")
                        self.main_window.progress_display.log_success(f"Organized {len(files)} {category} files")

                messagebox.showinfo(
                    "Organization Complete",
                    f"Files organized successfully!\n\n" + "\n".join(summary)
                )

            except Exception as e:
                self.logger.error(f"File organization failed: {e}")
                self.main_window.progress_display.log_error(f"Organization failed: {e}")
                messagebox.showerror("Organization Error", f"Failed to organize files: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=organize_task, daemon=True)
        thread.start()

    def _rename_files_workflow(self, file_manager):
        """Run file renaming workflow."""
        import threading
        from tkinter import filedialog, simpledialog

        def rename_task():
            try:
                # Get folder containing files to rename
                folder = filedialog.askdirectory(
                    title="Select folder with files to rename",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                # Get naming pattern
                pattern = simpledialog.askstring(
                    "Rename Pattern",
                    "Enter naming pattern:\n\n"
                    "Available variables:\n"
                    "- {counter:03d} = sequential number\n"
                    "- {original_name} = original filename\n"
                    "- {date} = current date\n"
                    "- {time} = current time\n\n"
                    "Example: video_{counter:03d}_{date}",
                    initialvalue="video_{counter:03d}_{date}"
                )

                if not pattern:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Batch renaming files in: {folder}")
                self.main_window.progress_display.update_progress(25, "Renaming files...")

                # Perform batch rename
                renamed_files = file_manager.batch_rename(folder, pattern, file_filter=".mp4")

                self.main_window.progress_display.update_progress(100, "Renaming complete")

                # Show results
                for file_path in renamed_files:
                    self.main_window.progress_display.log_success(f"Renamed: {Path(file_path).name}")

                messagebox.showinfo(
                    "Rename Complete",
                    f"Successfully renamed {len(renamed_files)} files!"
                )

            except Exception as e:
                self.logger.error(f"File renaming failed: {e}")
                self.main_window.progress_display.log_error(f"Renaming failed: {e}")
                messagebox.showerror("Rename Error", f"Failed to rename files: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=rename_task, daemon=True)
        thread.start()

    def _validate_files_workflow(self, file_manager):
        """Run file validation workflow."""
        import threading
        from tkinter import filedialog
        from pathlib import Path

        def validate_task():
            try:
                # Get folder to validate
                folder = filedialog.askdirectory(
                    title="Select folder with video files to validate",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Validating files in: {folder}")

                # Get video files
                video_files = []
                for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
                    video_files.extend(Path(folder).glob(f"*{ext}"))

                if not video_files:
                    messagebox.showinfo("No Files", "No video files found in the selected folder.")
                    self.enable_all_buttons()
                    return

                valid_files = 0
                invalid_files = 0

                for i, file_path in enumerate(video_files):
                    try:
                        progress = ((i + 1) / len(video_files)) * 100
                        self.main_window.progress_display.update_progress(
                            progress,
                            f"Validating: {file_path.name}"
                        )

                        # Validate file
                        file_info = file_manager.validate_file(str(file_path))

                        if file_info["validation"]["is_valid"]:
                            valid_files += 1
                            self.main_window.progress_display.log_success(
                                f"✓ Valid: {file_path.name} ({file_info['size_formatted']})"
                            )
                        else:
                            invalid_files += 1
                            errors = ", ".join(file_info["validation"]["errors"])
                            self.main_window.progress_display.log_error(
                                f"✗ Invalid: {file_path.name} - {errors}"
                            )

                        # Show warnings if any
                        for warning in file_info["validation"]["warnings"]:
                            self.main_window.progress_display.log_warning(
                                f"⚠ {file_path.name}: {warning}"
                            )

                    except Exception as e:
                        invalid_files += 1
                        self.main_window.progress_display.log_error(f"Failed to validate {file_path.name}: {e}")

                self.main_window.progress_display.update_progress(100, "Validation complete")

                messagebox.showinfo(
                    "Validation Complete",
                    f"Validation results:\n\n"
                    f"Valid files: {valid_files}\n"
                    f"Invalid files: {invalid_files}\n"
                    f"Total files: {len(video_files)}"
                )

            except Exception as e:
                self.logger.error(f"File validation failed: {e}")
                self.main_window.progress_display.log_error(f"Validation failed: {e}")
                messagebox.showerror("Validation Error", f"Failed to validate files: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=validate_task, daemon=True)
        thread.start()

    def _find_duplicates_workflow(self, file_manager):
        """Run duplicate file detection workflow."""
        import threading
        from tkinter import filedialog

        def duplicates_task():
            try:
                # Get folder to scan
                folder = filedialog.askdirectory(
                    title="Select folder to scan for duplicates",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Scanning for duplicates in: {folder}")
                self.main_window.progress_display.update_progress(50, "Scanning for duplicates...")

                # Find duplicates
                duplicates = file_manager.find_duplicates(folder)

                self.main_window.progress_display.update_progress(100, "Scan complete")

                if not duplicates:
                    self.main_window.progress_display.log_success("No duplicate files found!")
                    messagebox.showinfo("Duplicates Scan", "No duplicate files found!")
                else:
                    # Show duplicate results
                    total_duplicates = sum(len(files) for files in duplicates.values())
                    self.main_window.progress_display.log_warning(f"Found {len(duplicates)} sets of duplicates ({total_duplicates} files)")

                    for i, (hash_val, files) in enumerate(duplicates.items(), 1):
                        self.main_window.progress_display.log_info(f"Duplicate set {i}:")
                        for file_path in files:
                            self.main_window.progress_display.log_info(f"  - {Path(file_path).name}")

                    messagebox.showwarning(
                        "Duplicates Found",
                        f"Found {len(duplicates)} sets of duplicate files!\n"
                        f"Total duplicate files: {total_duplicates}\n\n"
                        f"Check the log for details."
                    )

            except Exception as e:
                self.logger.error(f"Duplicate detection failed: {e}")
                self.main_window.progress_display.log_error(f"Duplicate detection failed: {e}")
                messagebox.showerror("Duplicate Detection Error", f"Failed to detect duplicates: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=duplicates_task, daemon=True)
        thread.start()

    def _cleanup_folders_workflow(self, file_manager):
        """Run folder cleanup workflow."""
        import threading
        from tkinter import filedialog

        def cleanup_task():
            try:
                # Get folder to clean up
                folder = filedialog.askdirectory(
                    title="Select folder to clean up empty subfolders",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Cleaning up empty folders in: {folder}")
                self.main_window.progress_display.update_progress(50, "Cleaning up folders...")

                # Clean up empty folders
                removed_count = file_manager.cleanup_empty_folders(folder)

                self.main_window.progress_display.update_progress(100, "Cleanup complete")

                if removed_count > 0:
                    self.main_window.progress_display.log_success(f"Removed {removed_count} empty folders")
                    messagebox.showinfo("Cleanup Complete", f"Removed {removed_count} empty folders!")
                else:
                    self.main_window.progress_display.log_info("No empty folders found")
                    messagebox.showinfo("Cleanup Complete", "No empty folders found to remove.")

            except Exception as e:
                self.logger.error(f"Folder cleanup failed: {e}")
                self.main_window.progress_display.log_error(f"Cleanup failed: {e}")
                messagebox.showerror("Cleanup Error", f"Failed to clean up folders: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=cleanup_task, daemon=True)
        thread.start()

    def _folder_stats_workflow(self, file_manager):
        """Run folder statistics workflow."""
        import threading
        from tkinter import filedialog

        def stats_task():
            try:
                # Get folder to analyze
                folder = filedialog.askdirectory(
                    title="Select folder to analyze",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Analyzing folder: {folder}")
                self.main_window.progress_display.update_progress(50, "Analyzing folder...")

                # Get folder statistics
                stats = file_manager.get_folder_stats(folder)

                self.main_window.progress_display.update_progress(100, "Analysis complete")

                # Display statistics
                self.main_window.progress_display.log_info(f"Total files: {stats['total_files']}")
                self.main_window.progress_display.log_info(f"Total size: {stats['total_size_formatted']}")

                if stats['file_types']:
                    self.main_window.progress_display.log_info("File types:")
                    for ext, count in sorted(stats['file_types'].items()):
                        self.main_window.progress_display.log_info(f"  {ext or 'no extension'}: {count} files")

                if stats['largest_file']:
                    self.main_window.progress_display.log_info(
                        f"Largest file: {Path(stats['largest_file']['path']).name} "
                        f"({stats['largest_file']['size_formatted']})"
                    )

                # Show summary dialog
                summary = (
                    f"Folder Statistics\n\n"
                    f"Total files: {stats['total_files']}\n"
                    f"Total size: {stats['total_size_formatted']}\n"
                    f"File types: {len(stats['file_types'])}\n\n"
                    f"Check the log for detailed breakdown."
                )

                messagebox.showinfo("Folder Statistics", summary)

            except Exception as e:
                self.logger.error(f"Folder analysis failed: {e}")
                self.main_window.progress_display.log_error(f"Analysis failed: {e}")
                messagebox.showerror("Analysis Error", f"Failed to analyze folder: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=stats_task, daemon=True)
        thread.start()

    def _run_deepseek_workflow(self):
        """Run the DeepSeek AI workflow."""
        import threading
        from modules.deepseek_client import DeepSeekClient
        from tkinter import simpledialog, filedialog

        def deepseek_task():
            try:
                # Disable buttons during execution
                self.disable_all_buttons()

                # Get DeepSeek client
                deepseek_client = DeepSeekClient(self.main_window.settings)

                # Authenticate with DeepSeek
                self.main_window.progress_display.log_info("Authenticating with DeepSeek API...")
                if not deepseek_client.authenticate():
                    self.main_window.progress_display.log_error("Failed to authenticate with DeepSeek API")
                    messagebox.showerror("DeepSeek Error", "Failed to authenticate with DeepSeek API. Please check your API key in settings.")
                    return

                self.main_window.progress_display.log_success("Connected to DeepSeek API")

                # Get operation choice from user
                self.main_window.root.after(0, lambda: self._get_deepseek_operation(deepseek_client))

            except Exception as e:
                self.logger.error(f"DeepSeek workflow failed: {e}")
                self.main_window.progress_display.log_error(f"DeepSeek workflow failed: {e}")
                messagebox.showerror("DeepSeek Error", f"DeepSeek workflow failed: {e}")
            finally:
                self.enable_all_buttons()

        # Run in separate thread
        thread = threading.Thread(target=deepseek_task, daemon=True)
        thread.start()

    def _get_deepseek_operation(self, deepseek_client):
        """Get DeepSeek operation choice from user."""
        from tkinter import simpledialog

        try:
            # Show operation menu
            operations = [
                "1. Generate smart titles for files",
                "2. Create descriptions for videos",
                "3. Analyze file content",
                "4. Suggest better filenames",
                "5. Batch analyze multiple files",
                "6. Test API connection"
            ]

            choice = simpledialog.askstring(
                "DeepSeek AI Operations",
                "Choose an AI operation:\n\n" + "\n".join(operations) + "\n\nEnter number (1-6):",
                initialvalue="1"
            )

            if choice is None:  # User cancelled
                self.enable_all_buttons()
                return

            # Execute chosen operation
            if choice == "1":
                self._generate_titles_workflow(deepseek_client)
            elif choice == "2":
                self._generate_descriptions_workflow(deepseek_client)
            elif choice == "3":
                self._analyze_content_workflow(deepseek_client)
            elif choice == "4":
                self._suggest_filenames_workflow(deepseek_client)
            elif choice == "5":
                self._batch_analyze_workflow(deepseek_client)
            elif choice == "6":
                self._test_api_workflow(deepseek_client)
            else:
                messagebox.showwarning("Invalid Choice", "Please enter a number between 1 and 6")
                self.enable_all_buttons()

        except Exception as e:
            self.logger.error(f"Failed to get DeepSeek operation: {e}")
            self.enable_all_buttons()

    def _generate_titles_workflow(self, deepseek_client):
        """Run title generation workflow."""
        import threading
        from tkinter import filedialog, simpledialog
        from pathlib import Path

        def title_task():
            try:
                # Get folder with files to rename
                folder = filedialog.askdirectory(
                    title="Select folder with files to generate titles for",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                # Get video files
                video_files = []
                for ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
                    video_files.extend(Path(folder).glob(f"*{ext}"))

                if not video_files:
                    messagebox.showinfo("No Files", "No video files found in the selected folder.")
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Generating titles for {len(video_files)} files...")

                successful_renames = 0

                for i, file_path in enumerate(video_files):
                    try:
                        progress = ((i + 1) / len(video_files)) * 100
                        self.main_window.progress_display.update_progress(
                            progress,
                            f"Processing: {file_path.name}"
                        )

                        # Generate title from filename
                        title = deepseek_client.generate_title(
                            file_path.stem,
                            content_type="filename",
                            style="descriptive"
                        )

                        if title:
                            # Use file manager to rename
                            from modules.file_manager import FileManager
                            file_manager = FileManager(self.main_window.settings)

                            new_path = file_manager.smart_rename(
                                str(file_path),
                                title=title
                            )

                            successful_renames += 1
                            self.main_window.progress_display.log_success(
                                f"Renamed: {file_path.name} → {Path(new_path).name}"
                            )
                        else:
                            self.main_window.progress_display.log_warning(f"No title generated for: {file_path.name}")

                    except Exception as e:
                        self.main_window.progress_display.log_error(f"Failed to process {file_path.name}: {e}")
                        continue

                self.main_window.progress_display.update_progress(100, "Title generation complete")

                messagebox.showinfo(
                    "Title Generation Complete",
                    f"Successfully generated titles and renamed {successful_renames} out of {len(video_files)} files!"
                )

            except Exception as e:
                self.logger.error(f"Title generation failed: {e}")
                self.main_window.progress_display.log_error(f"Title generation failed: {e}")
                messagebox.showerror("Title Generation Error", f"Failed to generate titles: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=title_task, daemon=True)
        thread.start()

    def _generate_descriptions_workflow(self, deepseek_client):
        """Run description generation workflow."""
        import threading
        from tkinter import simpledialog

        def description_task():
            try:
                # Get title from user
                title = simpledialog.askstring(
                    "Description Generation",
                    "Enter the title or topic for description generation:",
                    initialvalue=""
                )

                if not title:
                    self.enable_all_buttons()
                    return

                # Get additional context
                context = simpledialog.askstring(
                    "Additional Context",
                    "Enter any additional context or hints (optional):",
                    initialvalue=""
                )

                self.main_window.progress_display.log_info(f"Generating description for: {title}")
                self.main_window.progress_display.update_progress(50, "Generating description...")

                # Generate description
                description = deepseek_client.generate_description(
                    title=title,
                    content_hints=context,
                    max_length=500,
                    style="engaging"
                )

                self.main_window.progress_display.update_progress(100, "Description generated")
                self.main_window.progress_display.log_success("Description generated successfully!")
                self.main_window.progress_display.log_info(f"Generated description:\n{description}")

                # Show description in a dialog
                from tkinter import scrolledtext
                desc_window = tk.Toplevel(self.main_window.root)
                desc_window.title("Generated Description")
                desc_window.geometry("600x400")

                text_widget = scrolledtext.ScrolledText(desc_window, wrap=tk.WORD)
                text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                text_widget.insert(tk.END, description)
                text_widget.config(state=tk.DISABLED)

                # Copy button
                def copy_description():
                    desc_window.clipboard_clear()
                    desc_window.clipboard_append(description)
                    messagebox.showinfo("Copied", "Description copied to clipboard!")

                copy_btn = ttk.Button(desc_window, text="Copy to Clipboard", command=copy_description)
                copy_btn.pack(pady=5)

            except Exception as e:
                self.logger.error(f"Description generation failed: {e}")
                self.main_window.progress_display.log_error(f"Description generation failed: {e}")
                messagebox.showerror("Description Error", f"Failed to generate description: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=description_task, daemon=True)
        thread.start()

    def _analyze_content_workflow(self, deepseek_client):
        """Run content analysis workflow."""
        import threading
        from tkinter import simpledialog

        def analysis_task():
            try:
                # Get content from user
                content = simpledialog.askstring(
                    "Content Analysis",
                    "Enter content to analyze (filename, description, or text):",
                    initialvalue=""
                )

                if not content:
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Analyzing content: {content[:50]}...")
                self.main_window.progress_display.update_progress(50, "Analyzing content...")

                # Analyze content
                analysis = deepseek_client.analyze_content(
                    content=content,
                    content_type="text",
                    extract_keywords=True
                )

                self.main_window.progress_display.update_progress(100, "Analysis complete")
                self.main_window.progress_display.log_success("Content analysis completed!")

                # Display results
                if analysis.get("main_topics"):
                    self.main_window.progress_display.log_info(f"Main topics: {', '.join(analysis['main_topics'])}")
                if analysis.get("keywords"):
                    self.main_window.progress_display.log_info(f"Keywords: {', '.join(analysis['keywords'])}")
                if analysis.get("category"):
                    self.main_window.progress_display.log_info(f"Category: {analysis['category']}")
                if analysis.get("summary"):
                    self.main_window.progress_display.log_info(f"Summary: {analysis['summary']}")

                # Show detailed results
                results_text = f"""Content Analysis Results:

Summary: {analysis.get('summary', 'N/A')}

Main Topics: {', '.join(analysis.get('main_topics', []))}

Keywords: {', '.join(analysis.get('keywords', []))}

Category: {analysis.get('category', 'N/A')}

Analysis completed at: {analysis.get('analyzed_at', 'N/A')}
"""

                messagebox.showinfo("Analysis Results", results_text)

            except Exception as e:
                self.logger.error(f"Content analysis failed: {e}")
                self.main_window.progress_display.log_error(f"Content analysis failed: {e}")
                messagebox.showerror("Analysis Error", f"Failed to analyze content: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=analysis_task, daemon=True)
        thread.start()

    def _suggest_filenames_workflow(self, deepseek_client):
        """Run filename suggestion workflow."""
        import threading
        from tkinter import filedialog
        from pathlib import Path

        def suggestion_task():
            try:
                # Get file to analyze
                file_path = filedialog.askopenfilename(
                    title="Select file to get naming suggestions for",
                    initialdir=self.main_window.settings.get('email.download_folder', ''),
                    filetypes=[("All files", "*.*")]
                )

                if not file_path:
                    self.enable_all_buttons()
                    return

                filename = Path(file_path).name
                self.main_window.progress_display.log_info(f"Generating filename suggestions for: {filename}")
                self.main_window.progress_display.update_progress(50, "Generating suggestions...")

                # Get suggestions
                suggestions = deepseek_client.suggest_filename(filename)

                self.main_window.progress_display.update_progress(100, "Suggestions generated")

                if suggestions:
                    self.main_window.progress_display.log_success(f"Generated {len(suggestions)} filename suggestions:")
                    for i, suggestion in enumerate(suggestions, 1):
                        self.main_window.progress_display.log_info(f"{i}. {suggestion}")

                    # Show suggestions in dialog
                    suggestions_text = f"Filename suggestions for: {filename}\n\n"
                    for i, suggestion in enumerate(suggestions, 1):
                        suggestions_text += f"{i}. {suggestion}\n"

                    messagebox.showinfo("Filename Suggestions", suggestions_text)
                else:
                    self.main_window.progress_display.log_warning("No suggestions generated")
                    messagebox.showinfo("No Suggestions", "No filename suggestions could be generated.")

            except Exception as e:
                self.logger.error(f"Filename suggestion failed: {e}")
                self.main_window.progress_display.log_error(f"Filename suggestion failed: {e}")
                messagebox.showerror("Suggestion Error", f"Failed to generate suggestions: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=suggestion_task, daemon=True)
        thread.start()

    def _batch_analyze_workflow(self, deepseek_client):
        """Run batch analysis workflow."""
        import threading
        from tkinter import filedialog
        from pathlib import Path

        def batch_task():
            try:
                # Get folder to analyze
                folder = filedialog.askdirectory(
                    title="Select folder with files to analyze",
                    initialdir=self.main_window.settings.get('email.download_folder', '')
                )

                if not folder:
                    self.enable_all_buttons()
                    return

                # Get all files
                all_files = [f for f in Path(folder).iterdir() if f.is_file()]

                if not all_files:
                    messagebox.showinfo("No Files", "No files found in the selected folder.")
                    self.enable_all_buttons()
                    return

                self.main_window.progress_display.log_info(f"Batch analyzing {len(all_files)} files...")

                def progress_callback(current, total):
                    progress = (current / total) * 100
                    self.main_window.progress_display.update_progress(progress, f"Analyzing file {current}/{total}")

                # Perform batch analysis
                results = deepseek_client.batch_analyze_files(
                    [str(f) for f in all_files],
                    progress_callback=progress_callback
                )

                self.main_window.progress_display.update_progress(100, "Batch analysis complete")

                # Display results summary
                successful = sum(1 for r in results.values() if "error" not in r)
                failed = len(results) - successful

                self.main_window.progress_display.log_success(f"Batch analysis completed: {successful} successful, {failed} failed")

                # Show detailed results for successful analyses
                for file_path, analysis in results.items():
                    if "error" not in analysis:
                        filename = Path(file_path).name
                        topics = analysis.get("main_topics", [])
                        if topics:
                            self.main_window.progress_display.log_info(f"{filename}: {', '.join(topics[:3])}")
                    else:
                        self.main_window.progress_display.log_error(f"{Path(file_path).name}: {analysis['error']}")

                messagebox.showinfo(
                    "Batch Analysis Complete",
                    f"Analyzed {len(all_files)} files:\n"
                    f"Successful: {successful}\n"
                    f"Failed: {failed}\n\n"
                    f"Check the log for detailed results."
                )

            except Exception as e:
                self.logger.error(f"Batch analysis failed: {e}")
                self.main_window.progress_display.log_error(f"Batch analysis failed: {e}")
                messagebox.showerror("Batch Analysis Error", f"Failed to perform batch analysis: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=batch_task, daemon=True)
        thread.start()

    def _test_api_workflow(self, deepseek_client):
        """Run API connection test workflow."""
        import threading

        def test_task():
            try:
                self.main_window.progress_display.log_info("Testing DeepSeek API connection...")
                self.main_window.progress_display.update_progress(50, "Testing API...")

                # Test API connection
                test_result = deepseek_client.test_api_connection()

                self.main_window.progress_display.update_progress(100, "Test complete")

                if test_result["success"]:
                    response_time = test_result.get("response_time", 0)
                    self.main_window.progress_display.log_success(f"API test successful! Response time: {response_time:.2f}s")

                    # Get usage stats
                    stats = deepseek_client.get_usage_stats()
                    self.main_window.progress_display.log_info(f"Requests made: {stats['requests_made']}")
                    self.main_window.progress_display.log_info(f"Tokens used: {stats['tokens_used']}")

                    messagebox.showinfo(
                        "API Test Successful",
                        f"DeepSeek API is working correctly!\n\n"
                        f"Response time: {response_time:.2f} seconds\n"
                        f"Requests made: {stats['requests_made']}\n"
                        f"Tokens used: {stats['tokens_used']}"
                    )
                else:
                    error = test_result.get("error", "Unknown error")
                    self.main_window.progress_display.log_error(f"API test failed: {error}")
                    messagebox.showerror("API Test Failed", f"DeepSeek API test failed:\n{error}")

            except Exception as e:
                self.logger.error(f"API test failed: {e}")
                self.main_window.progress_display.log_error(f"API test failed: {e}")
                messagebox.showerror("Test Error", f"Failed to test API: {e}")
            finally:
                self.enable_all_buttons()
                self.main_window.progress_display.reset_progress()

        thread = threading.Thread(target=test_task, daemon=True)
        thread.start()
