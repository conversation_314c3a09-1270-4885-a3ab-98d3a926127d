"""
Tests for DeepSeek Client

This module contains tests for the DeepSeek AI integration functionality.
"""

import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from modules.deepseek_client import DeepSeekClient
from config.settings import Settings
from utils.error_handler import DeepSeekError

class TestDeepSeekClient(unittest.TestCase):
    """Test cases for DeepSeekClient class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_file = Path(self.temp_dir) / "test_settings.json"
        
        # Create settings instance with test config file
        self.settings = Settings(str(self.test_config_file))
        
        # Configure test DeepSeek settings
        self.settings.set('deepseek.api_key', 'sk-test-key-1234567890123456789')
        self.settings.set('deepseek.base_url', 'https://api.deepseek.com')
        self.settings.set('deepseek.model', 'deepseek-chat')
        self.settings.set('deepseek.max_tokens', 1000)
        self.settings.set('deepseek.temperature', 0.7)
        
        # Create DeepSeek client
        self.deepseek_client = DeepSeekClient(self.settings)
        
    def tearDown(self):
        """Clean up test environment."""
        # Clean up temporary files
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_initialization(self):
        """Test DeepSeek client initialization."""
        self.assertIsNotNone(self.deepseek_client.settings)
        self.assertIsNotNone(self.deepseek_client.logger)
        self.assertEqual(self.deepseek_client.model, 'deepseek-chat')
        self.assertEqual(self.deepseek_client.max_tokens, 1000)
        self.assertEqual(self.deepseek_client.temperature, 0.7)
        self.assertFalse(self.deepseek_client.is_authenticated)
        
    @patch('modules.deepseek_client.api_keys')
    def test_authentication_no_key(self, mock_api_keys):
        """Test authentication with no API key."""
        mock_api_keys.get_key.return_value = None
        
        with self.assertRaises(DeepSeekError):
            self.deepseek_client.authenticate()
            
    @patch('modules.deepseek_client.api_keys')
    @patch('modules.deepseek_client.OpenAI')
    def test_authentication_success(self, mock_openai, mock_api_keys):
        """Test successful authentication."""
        # Mock API key
        mock_api_keys.get_key.return_value = 'sk-test-key-1234567890123456789'
        
        # Mock OpenAI client
        mock_client = MagicMock()
        mock_openai.return_value = mock_client
        
        # Mock successful test connection
        with patch.object(self.deepseek_client, '_test_connection', return_value=True):
            result = self.deepseek_client.authenticate()
            
        self.assertTrue(result)
        self.assertTrue(self.deepseek_client.is_authenticated)
        self.assertIsNotNone(self.deepseek_client.client)
        
    @patch('modules.deepseek_client.api_keys')
    def test_authentication_invalid_key(self, mock_api_keys):
        """Test authentication with invalid API key."""
        mock_api_keys.get_key.return_value = 'invalid-key'
        
        with self.assertRaises(DeepSeekError):
            self.deepseek_client.authenticate()
            
    def test_make_api_request_not_authenticated(self):
        """Test API request when not authenticated."""
        messages = [{"role": "user", "content": "test"}]
        
        with self.assertRaises(DeepSeekError):
            self.deepseek_client._make_api_request(messages)
            
    @patch('modules.deepseek_client.OpenAI')
    def test_make_api_request_success(self, mock_openai):
        """Test successful API request."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock OpenAI client and response
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_choice.message.content = "Test response"
        mock_response.choices = [mock_choice]
        mock_response.usage.total_tokens = 50
        mock_client.chat.completions.create.return_value = mock_response
        self.deepseek_client.client = mock_client
        
        messages = [{"role": "user", "content": "test"}]
        result = self.deepseek_client._make_api_request(messages)
        
        self.assertEqual(result, "Test response")
        self.assertEqual(self.deepseek_client.usage_stats["tokens_used"], 50)
        
    def test_build_analysis_prompt(self):
        """Test analysis prompt building."""
        content = "test_video.mp4"
        prompt = self.deepseek_client._build_analysis_prompt(content, "filename", True)
        
        self.assertIn(content, prompt)
        self.assertIn("filename", prompt)
        self.assertIn("keywords", prompt.lower())
        
    def test_parse_analysis_response(self):
        """Test analysis response parsing."""
        response = """
        Main topics: Technology, AI, Machine Learning
        Keywords: artificial intelligence, deep learning, neural networks
        Category: Educational
        This is a summary of the content.
        """
        
        analysis = self.deepseek_client._parse_analysis_response(response)
        
        self.assertIn("main_topics", analysis)
        self.assertIn("keywords", analysis)
        self.assertIn("category", analysis)
        self.assertTrue(len(analysis["main_topics"]) > 0)
        self.assertTrue(len(analysis["keywords"]) > 0)
        
    @patch.object(DeepSeekClient, '_make_api_request')
    def test_analyze_content(self, mock_request):
        """Test content analysis."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock API response
        mock_request.return_value = """
        Main topics: Video editing, Tutorial
        Keywords: editing, tutorial, beginner
        Category: Educational
        """
        
        result = self.deepseek_client.analyze_content("video_tutorial.mp4", "filename")
        
        self.assertIn("main_topics", result)
        self.assertIn("keywords", result)
        self.assertIn("original_content", result)
        self.assertEqual(result["content_type"], "filename")
        
    def test_build_title_prompt(self):
        """Test title generation prompt building."""
        content = "machine learning tutorial"
        prompt = self.deepseek_client._build_title_prompt(content, "video", 100, "engaging")
        
        self.assertIn(content, prompt)
        self.assertIn("100", prompt)
        self.assertIn("engaging", prompt.lower())
        
    def test_clean_title(self):
        """Test title cleaning."""
        # Test with quotes
        title = '"This is a test title"'
        cleaned = self.deepseek_client._clean_title(title, 100)
        self.assertEqual(cleaned, "This is a test title")
        
        # Test with prefix
        title = "Title: Great Video Content"
        cleaned = self.deepseek_client._clean_title(title, 100)
        self.assertEqual(cleaned, "Great Video Content")
        
        # Test with invalid characters
        title = "Test<>Title|With?Invalid*Chars"
        cleaned = self.deepseek_client._clean_title(title, 100)
        self.assertNotIn('<', cleaned)
        self.assertNotIn('>', cleaned)
        self.assertNotIn('|', cleaned)
        
        # Test length truncation
        long_title = "This is a very long title that exceeds the maximum length limit"
        cleaned = self.deepseek_client._clean_title(long_title, 30)
        self.assertLessEqual(len(cleaned), 35)  # Allow for ellipsis
        
    @patch.object(DeepSeekClient, '_make_api_request')
    def test_generate_title(self, mock_request):
        """Test title generation."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock API response
        mock_request.return_value = "Amazing Machine Learning Tutorial"
        
        result = self.deepseek_client.generate_title("ml_tutorial_video.mp4", "video")
        
        self.assertEqual(result, "Amazing Machine Learning Tutorial")
        mock_request.assert_called_once()
        
    def test_build_description_prompt(self):
        """Test description generation prompt building."""
        title = "Machine Learning Basics"
        hints = "Beginner-friendly tutorial"
        prompt = self.deepseek_client._build_description_prompt(title, hints, 500, True, "informative")
        
        self.assertIn(title, prompt)
        self.assertIn(hints, prompt)
        self.assertIn("500", prompt)
        self.assertIn("keywords", prompt.lower())
        
    def test_clean_description(self):
        """Test description cleaning."""
        # Test with prefix
        description = "Description: This is a great video about machine learning."
        cleaned = self.deepseek_client._clean_description(description, 500)
        self.assertEqual(cleaned, "This is a great video about machine learning.")
        
        # Test length truncation
        long_description = "This is a very long description. " * 20
        cleaned = self.deepseek_client._clean_description(long_description, 100)
        self.assertLessEqual(len(cleaned), 105)  # Allow for ellipsis
        
    @patch.object(DeepSeekClient, '_make_api_request')
    def test_generate_description(self, mock_request):
        """Test description generation."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock API response
        mock_request.return_value = "This is a comprehensive tutorial on machine learning basics."
        
        result = self.deepseek_client.generate_description("Machine Learning Tutorial")
        
        self.assertEqual(result, "This is a comprehensive tutorial on machine learning basics.")
        mock_request.assert_called_once()
        
    @patch.object(DeepSeekClient, '_make_api_request')
    def test_suggest_filename(self, mock_request):
        """Test filename suggestion."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock API response
        mock_request.return_value = """
        machine_learning_tutorial.mp4
        ml_basics_explained.mp4
        beginner_ai_guide.mp4
        """
        
        result = self.deepseek_client.suggest_filename("video123.mp4")
        
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)
        self.assertTrue(any("machine_learning" in suggestion for suggestion in result))
        
    def test_get_api_status(self):
        """Test API status retrieval."""
        status = self.deepseek_client.get_api_status()
        
        self.assertIn("authenticated", status)
        self.assertIn("api_available", status)
        self.assertIn("model", status)
        self.assertIn("usage_stats", status)
        
    def test_get_usage_stats(self):
        """Test usage statistics retrieval."""
        stats = self.deepseek_client.get_usage_stats()
        
        self.assertIn("requests_made", stats)
        self.assertIn("tokens_used", stats)
        self.assertIn("errors", stats)
        self.assertIn("last_request", stats)
        
    def test_reset_usage_stats(self):
        """Test usage statistics reset."""
        # Set some usage stats
        self.deepseek_client.usage_stats["requests_made"] = 10
        self.deepseek_client.usage_stats["tokens_used"] = 500
        
        # Reset stats
        self.deepseek_client.reset_usage_stats()
        
        # Verify reset
        self.assertEqual(self.deepseek_client.usage_stats["requests_made"], 0)
        self.assertEqual(self.deepseek_client.usage_stats["tokens_used"], 0)
        
    @patch.object(DeepSeekClient, '_make_api_request')
    def test_test_api_connection(self, mock_request):
        """Test API connection testing."""
        # Set up authentication
        self.deepseek_client.is_authenticated = True
        
        # Mock successful response
        mock_request.return_value = "OK"
        
        result = self.deepseek_client.test_api_connection()
        
        self.assertTrue(result["success"])
        self.assertIsNotNone(result["response_time"])
        self.assertIsNone(result["error"])
        
    def test_batch_analyze_files(self):
        """Test batch file analysis."""
        # Create test files
        test_files = []
        for i in range(3):
            test_file = Path(self.temp_dir) / f"test_file_{i}.mp4"
            test_file.write_text("test content")
            test_files.append(str(test_file))
            
        # Mock analyze_content method
        with patch.object(self.deepseek_client, 'analyze_content') as mock_analyze:
            mock_analyze.return_value = {"main_topics": ["test"], "keywords": ["video"]}
            
            results = self.deepseek_client.batch_analyze_files(test_files)
            
        self.assertEqual(len(results), 3)
        for file_path in test_files:
            self.assertIn(file_path, results)
            self.assertIn("main_topics", results[file_path])

if __name__ == '__main__':
    unittest.main()
