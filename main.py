#!/usr/bin/env python3
"""
Desktop Assistant - Main Application Entry Point

A desktop application that performs various tasks including:
- Email checking and video downloading
- File management and renaming
- DeepSeek integration for content generation
- YouTube video uploading

Author: Desktop Assistant Project
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_window import MainWindow
from utils.logger import setup_logger
from config.settings import Settings

def main():
    """Main application entry point."""
    try:
        # Set up logging
        logger = setup_logger()
        logger.info("Starting Desktop Assistant application...")
        
        # Load configuration
        settings = Settings()
        
        # Create and run the main application
        app = MainWindow(settings)
        app.run()
        
    except Exception as e:
        print(f"Failed to start application: {e}")
        logging.error(f"Application startup failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
