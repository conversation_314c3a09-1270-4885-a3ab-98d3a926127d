"""
Progress Display for Desktop Assistant

This module contains the progress display panel for showing task progress and logs.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import logging
from datetime import datetime
from typing import Optional

class ProgressDisplay(ttk.Frame):
    """Panel for displaying task progress and application logs."""
    
    def __init__(self, parent):
        """Initialize the progress display panel.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        self._setup_display()
        
    def _setup_display(self):
        """Set up the progress display components."""
        # Title for the panel
        title_label = ttk.Label(self, text="Progress & Logs", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Progress bar frame
        progress_frame = ttk.Frame(self)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Progress bar label
        self.progress_label = ttk.Label(progress_frame, text="Ready")
        self.progress_label.pack(anchor=tk.W)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Log display frame
        log_frame = ttk.LabelFrame(self, text="Activity Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Log text area with scrollbar
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            wrap=tk.WORD,
            width=50,
            height=20,
            font=("Consolas", 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Control buttons frame
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Clear log button
        self.clear_btn = ttk.Button(
            control_frame,
            text="Clear Log",
            command=self._clear_log
        )
        self.clear_btn.pack(side=tk.LEFT)
        
        # Save log button
        self.save_btn = ttk.Button(
            control_frame,
            text="Save Log",
            command=self._save_log
        )
        self.save_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Add initial welcome message
        self._add_log_entry("Desktop Assistant started successfully", "INFO")
        
    def update_progress(self, value: float, message: str = ""):
        """Update the progress bar and message.
        
        Args:
            value: Progress value (0-100)
            message: Progress message to display
        """
        self.progress_var.set(value)
        if message:
            self.progress_label.config(text=message)
        self.update_idletasks()
        
    def reset_progress(self):
        """Reset the progress bar to 0."""
        self.progress_var.set(0)
        self.progress_label.config(text="Ready")
        
    def _add_log_entry(self, message: str, level: str = "INFO"):
        """Add an entry to the log display.
        
        Args:
            message: Log message
            level: Log level (INFO, WARNING, ERROR, etc.)
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        # Insert at the end
        self.log_text.insert(tk.END, log_entry)
        
        # Auto-scroll to bottom
        self.log_text.see(tk.END)
        
        # Color coding based on level
        if level == "ERROR":
            # Make error messages red
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            self.log_text.tag_add("error", start_line, end_line)
            self.log_text.tag_config("error", foreground="red")
        elif level == "WARNING":
            # Make warning messages orange
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            self.log_text.tag_add("warning", start_line, end_line)
            self.log_text.tag_config("warning", foreground="orange")
        elif level == "SUCCESS":
            # Make success messages green
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            self.log_text.tag_add("success", start_line, end_line)
            self.log_text.tag_config("success", foreground="green")
            
    def log_info(self, message: str):
        """Log an info message."""
        self._add_log_entry(message, "INFO")
        
    def log_warning(self, message: str):
        """Log a warning message."""
        self._add_log_entry(message, "WARNING")
        
    def log_error(self, message: str):
        """Log an error message."""
        self._add_log_entry(message, "ERROR")
        
    def log_success(self, message: str):
        """Log a success message."""
        self._add_log_entry(message, "SUCCESS")
        
    def _clear_log(self):
        """Clear the log display."""
        self.log_text.delete(1.0, tk.END)
        self._add_log_entry("Log cleared", "INFO")
        
    def _save_log(self):
        """Save the log to a file."""
        from tkinter import filedialog
        
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Save Log File"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    log_content = self.log_text.get(1.0, tk.END)
                    f.write(log_content)
                self._add_log_entry(f"Log saved to {filename}", "SUCCESS")
                
        except Exception as e:
            self._add_log_entry(f"Failed to save log: {e}", "ERROR")
