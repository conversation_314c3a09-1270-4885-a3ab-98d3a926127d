"""
Tests for Email Handler

This module contains tests for the email handling functionality.
"""

import unittest
import tempfile
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from modules.email_handler import <PERSON>ailHandler
from config.settings import Settings
from utils.error_handler import EmailError

class TestEmailHandler(unittest.TestCase):
    """Test cases for EmailHandler class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test settings
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_file = Path(self.temp_dir) / "test_settings.json"
        
        # Create settings instance with test config file
        self.settings = Settings(str(self.test_config_file))
        
        # Configure test email settings
        self.settings.set('email.server', 'imap.test.com')
        self.settings.set('email.port', 993)
        self.settings.set('email.username', '<EMAIL>')
        self.settings.set('email.use_ssl', True)
        self.settings.set('email.download_folder', str(Path(self.temp_dir) / "downloads"))
        
        # Create email handler
        self.email_handler = EmailHandler(self.settings)
        
    def tearDown(self):
        """Clean up test environment."""
        # Disconnect if connected
        if self.email_handler.is_connected():
            self.email_handler.disconnect()
            
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_initialization(self):
        """Test email handler initialization."""
        self.assertIsNotNone(self.email_handler.settings)
        self.assertIsNotNone(self.email_handler.logger)
        self.assertFalse(self.email_handler.is_connected())
        
    def test_get_server_config(self):
        """Test server configuration detection."""
        # Test Gmail configuration
        config = self.email_handler._get_server_config('<EMAIL>')
        self.assertEqual(config['server'], 'imap.gmail.com')
        self.assertEqual(config['port'], 993)
        self.assertTrue(config['ssl'])
        
        # Test Outlook configuration
        config = self.email_handler._get_server_config('<EMAIL>')
        self.assertEqual(config['server'], 'outlook.office365.com')
        
        # Test manual configuration
        config = self.email_handler._get_server_config('<EMAIL>')
        self.assertEqual(config['server'], 'imap.test.com')
        
    @patch('modules.email_handler.IMAPClient')
    @patch('modules.email_handler.api_keys')
    def test_connect_success(self, mock_api_keys, mock_imap_client):
        """Test successful email connection."""
        # Mock dependencies
        mock_api_keys.get_key.return_value = 'test_password'
        mock_connection = Mock()
        mock_imap_client.return_value = mock_connection
        
        # Test connection
        result = self.email_handler.connect()
        
        # Verify results
        self.assertTrue(result)
        self.assertTrue(self.email_handler.is_connected())
        mock_connection.login.assert_called_once_with('<EMAIL>', 'test_password')
        
    @patch('modules.email_handler.api_keys')
    def test_connect_no_username(self, mock_api_keys):
        """Test connection failure with no username."""
        # Clear username
        self.settings.set('email.username', '')
        
        # Test connection
        with self.assertRaises(EmailError) as context:
            self.email_handler.connect()
            
        self.assertIn("username not configured", str(context.exception))
        
    @patch('modules.email_handler.api_keys')
    def test_connect_no_password(self, mock_api_keys):
        """Test connection failure with no password."""
        # Mock no password
        mock_api_keys.get_key.return_value = None
        
        # Test connection
        with self.assertRaises(EmailError) as context:
            self.email_handler.connect()
            
        self.assertIn("password not configured", str(context.exception))
        
    def test_disconnect(self):
        """Test email disconnection."""
        # Mock connection
        mock_connection = Mock()
        self.email_handler.connection = mock_connection
        self.email_handler.is_authenticated = True
        self.email_handler.server = 'imapclient'
        
        # Test disconnect
        self.email_handler.disconnect()
        
        # Verify results
        self.assertFalse(self.email_handler.is_connected())
        self.assertIsNone(self.email_handler.connection)
        mock_connection.logout.assert_called_once()
        
    @patch('modules.email_handler.IMAPClient')
    @patch('modules.email_handler.api_keys')
    def test_search_emails(self, mock_api_keys, mock_imap_client):
        """Test email search functionality."""
        # Mock dependencies
        mock_api_keys.get_key.return_value = 'test_password'
        mock_connection = Mock()
        mock_imap_client.return_value = mock_connection
        
        # Mock search results
        mock_connection.search.return_value = [1, 2, 3]
        mock_connection.fetch.return_value = {
            1: {b'ENVELOPE': Mock(from_=[Mock()], subject=b'Test Subject 1', date=Mock()),
                b'BODYSTRUCTURE': Mock()},
            2: {b'ENVELOPE': Mock(from_=[Mock()], subject=b'Test Subject 2', date=Mock()),
                b'BODYSTRUCTURE': Mock()},
            3: {b'ENVELOPE': Mock(from_=[Mock()], subject=b'Test Subject 3', date=Mock()),
                b'BODYSTRUCTURE': Mock()}
        }
        
        # Connect and search
        self.email_handler.connect()
        emails = self.email_handler.search_emails(sender='<EMAIL>', limit=3)
        
        # Verify results
        self.assertEqual(len(emails), 3)
        mock_connection.select_folder.assert_called_with('INBOX')
        mock_connection.search.assert_called()
        
    def test_search_emails_not_connected(self):
        """Test email search when not connected."""
        with self.assertRaises(EmailError) as context:
            self.email_handler.search_emails()
            
        self.assertIn("Not connected", str(context.exception))
        
    @patch('modules.email_handler.IMAPClient')
    @patch('modules.email_handler.api_keys')
    def test_download_attachments(self, mock_api_keys, mock_imap_client):
        """Test attachment download functionality."""
        # Mock dependencies
        mock_api_keys.get_key.return_value = 'test_password'
        mock_connection = Mock()
        mock_imap_client.return_value = mock_connection
        
        # Mock email with attachment
        mock_email = Mock()
        mock_part = Mock()
        mock_part.get_content_disposition.return_value = 'attachment'
        mock_part.get_filename.return_value = 'test_video.mp4'
        mock_part.get_payload.return_value = b'fake_video_data'
        mock_email.walk.return_value = [mock_part]
        
        with patch('email.message_from_bytes', return_value=mock_email):
            mock_connection.fetch.return_value = {1: {b'RFC822': b'fake_email_data'}}
            
            # Connect and download
            self.email_handler.connect()
            downloaded_files = self.email_handler.download_attachments('1')
            
            # Verify results
            self.assertEqual(len(downloaded_files), 1)
            self.assertTrue(downloaded_files[0].endswith('test_video.mp4'))
            
    def test_download_attachments_not_connected(self):
        """Test attachment download when not connected."""
        with self.assertRaises(EmailError) as context:
            self.email_handler.download_attachments('1')
            
        self.assertIn("Not connected", str(context.exception))
        
    @patch('modules.email_handler.IMAPClient')
    @patch('modules.email_handler.api_keys')
    def test_get_email_count(self, mock_api_keys, mock_imap_client):
        """Test getting email count."""
        # Mock dependencies
        mock_api_keys.get_key.return_value = 'test_password'
        mock_connection = Mock()
        mock_imap_client.return_value = mock_connection
        mock_connection.search.return_value = [1, 2, 3, 4, 5]
        
        # Connect and get count
        self.email_handler.connect()
        count = self.email_handler.get_email_count()
        
        # Verify results
        self.assertEqual(count, 5)
        
    def test_get_email_count_not_connected(self):
        """Test getting email count when not connected."""
        count = self.email_handler.get_email_count()
        self.assertEqual(count, 0)
        
    @patch('modules.email_handler.IMAPClient')
    @patch('modules.email_handler.api_keys')
    def test_get_folders(self, mock_api_keys, mock_imap_client):
        """Test getting email folders."""
        # Mock dependencies
        mock_api_keys.get_key.return_value = 'test_password'
        mock_connection = Mock()
        mock_imap_client.return_value = mock_connection
        mock_connection.list_folders.return_value = [
            (None, None, 'INBOX'),
            (None, None, 'Sent'),
            (None, None, 'Drafts')
        ]
        
        # Connect and get folders
        self.email_handler.connect()
        folders = self.email_handler.get_folders()
        
        # Verify results
        self.assertIn('INBOX', folders)
        self.assertIn('Sent', folders)
        self.assertIn('Drafts', folders)
        
    def test_get_folders_not_connected(self):
        """Test getting folders when not connected."""
        folders = self.email_handler.get_folders()
        self.assertEqual(folders, ['INBOX'])

if __name__ == '__main__':
    unittest.main()
