"""
API Keys Management for Desktop Assistant

This module handles secure storage and retrieval of API keys.
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional, Dict
import base64

class APIKeyManager:
    """Secure API key management."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize API key manager.
        
        Args:
            config_dir: Configuration directory path
        """
        self.logger = logging.getLogger(__name__)
        
        if config_dir is None:
            self.config_dir = Path.home() / ".desktop_assistant"
        else:
            self.config_dir = config_dir
            
        self.keys_file = self.config_dir / "api_keys.json"
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Load existing keys
        self._keys = self._load_keys()
        
    def _load_keys(self) -> Dict[str, str]:
        """Load API keys from file.
        
        Returns:
            Dictionary of API keys
        """
        try:
            if self.keys_file.exists():
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    encrypted_keys = json.load(f)
                    
                # Decrypt keys (simple base64 for now, can be enhanced later)
                keys = {}
                for service, encrypted_key in encrypted_keys.items():
                    try:
                        keys[service] = base64.b64decode(encrypted_key.encode()).decode()
                    except Exception:
                        self.logger.warning(f"Failed to decrypt key for {service}")
                        keys[service] = ""
                        
                return keys
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"Failed to load API keys: {e}")
            return {}
            
    def _save_keys(self):
        """Save API keys to file."""
        try:
            # Encrypt keys (simple base64 for now)
            encrypted_keys = {}
            for service, key in self._keys.items():
                if key:
                    encrypted_keys[service] = base64.b64encode(key.encode()).decode()
                    
            with open(self.keys_file, 'w', encoding='utf-8') as f:
                json.dump(encrypted_keys, f, indent=2)
                
            # Set restrictive permissions on the file
            os.chmod(self.keys_file, 0o600)
            
        except Exception as e:
            self.logger.error(f"Failed to save API keys: {e}")
            
    def set_key(self, service: str, api_key: str):
        """Set an API key for a service.
        
        Args:
            service: Service name (e.g., 'deepseek', 'youtube')
            api_key: API key to store
        """
        self._keys[service] = api_key
        self._save_keys()
        self.logger.info(f"API key set for {service}")
        
    def get_key(self, service: str) -> Optional[str]:
        """Get an API key for a service.
        
        Args:
            service: Service name
            
        Returns:
            API key or None if not found
        """
        return self._keys.get(service)
        
    def remove_key(self, service: str):
        """Remove an API key for a service.
        
        Args:
            service: Service name
        """
        if service in self._keys:
            del self._keys[service]
            self._save_keys()
            self.logger.info(f"API key removed for {service}")
            
    def list_services(self) -> list:
        """List all services with stored API keys.
        
        Returns:
            List of service names
        """
        return list(self._keys.keys())
        
    def has_key(self, service: str) -> bool:
        """Check if an API key exists for a service.
        
        Args:
            service: Service name
            
        Returns:
            True if key exists, False otherwise
        """
        return service in self._keys and bool(self._keys[service])
        
    def validate_keys(self) -> Dict[str, bool]:
        """Validate all stored API keys.
        
        Returns:
            Dictionary mapping service names to validation status
        """
        validation_results = {}
        
        for service in self._keys:
            key = self._keys[service]
            if not key:
                validation_results[service] = False
                continue
                
            # Basic validation (length and format checks)
            if service == 'deepseek':
                # DeepSeek API keys typically start with 'sk-'
                validation_results[service] = key.startswith('sk-') and len(key) > 20
            elif service == 'youtube':
                # YouTube uses OAuth, so we check for client_id format
                validation_results[service] = len(key) > 10 and '.' in key
            else:
                # Generic validation - just check if not empty
                validation_results[service] = len(key) > 0
                
        return validation_results
        
    def clear_all_keys(self):
        """Clear all stored API keys."""
        self._keys.clear()
        if self.keys_file.exists():
            self.keys_file.unlink()
        self.logger.info("All API keys cleared")

# Global instance for easy access
api_keys = APIKeyManager()
