"""
Settings Management for Desktop Assistant

This module handles application settings and configuration.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

class Settings:
    """Application settings manager."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize settings manager.
        
        Args:
            config_file: Path to configuration file (optional)
        """
        self.logger = logging.getLogger(__name__)
        
        # Default configuration file path
        if config_file is None:
            self.config_dir = Path.home() / ".desktop_assistant"
            self.config_file = self.config_dir / "settings.json"
        else:
            self.config_file = Path(config_file)
            self.config_dir = self.config_file.parent
            
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Default settings
        self._default_settings = {
            "app": {
                "version": "1.0.0",
                "auto_save": True,
                "log_level": "INFO"
            },
            "email": {
                "server": "",
                "port": 993,
                "username": "",
                "use_ssl": True,
                "download_folder": str(Path.home() / "Downloads" / "DesktopAssistant"),
                "search_folders": ["INBOX"],
                "max_emails": 50,
                "attachment_types": [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"]
            },
            "deepseek": {
                "api_key": "",
                "base_url": "https://api.deepseek.com",
                "model": "deepseek-chat"
            },
            "youtube": {
                "client_id": "",
                "client_secret": "",
                "upload_folder": str(Path.home() / "Downloads" / "DesktopAssistant"),
                "default_privacy": "private",
                "default_category": "22"  # People & Blogs
            },
            "files": {
                "temp_folder": str(Path.home() / "Downloads" / "DesktopAssistant" / "temp"),
                "processed_folder": str(Path.home() / "Downloads" / "DesktopAssistant" / "processed"),
                "backup_folder": str(Path.home() / "Downloads" / "DesktopAssistant" / "backup"),
                "max_file_size_mb": 500,
                "auto_organize": True,
                "backup_before_rename": True,
                "allowed_extensions": [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"],
                "naming_pattern": "{title}_{date}",
                "date_format": "%Y%m%d_%H%M%S"
            }
        }
        
        # Load settings
        self.settings = self._load_settings()
        
    def _load_settings(self) -> Dict[str, Any]:
        """Load settings from file or create default settings.
        
        Returns:
            Dictionary containing settings
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                settings = self._merge_settings(self._default_settings, loaded_settings)
                self.logger.info(f"Settings loaded from {self.config_file}")
                return settings
            else:
                self.logger.info("No settings file found, using defaults")
                self._save_settings(self._default_settings)
                return self._default_settings.copy()
                
        except Exception as e:
            self.logger.error(f"Failed to load settings: {e}")
            self.logger.info("Using default settings")
            return self._default_settings.copy()
            
    def _merge_settings(self, defaults: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Merge loaded settings with defaults.

        Args:
            defaults: Default settings dictionary
            loaded: Loaded settings dictionary

        Returns:
            Merged settings dictionary
        """
        import copy
        merged = copy.deepcopy(defaults)

        for key, value in loaded.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key].update(value)
            else:
                merged[key] = value

        return merged
        
    def _save_settings(self, settings: Dict[str, Any]):
        """Save settings to file.
        
        Args:
            settings: Settings dictionary to save
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Settings saved to {self.config_file}")
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")
            
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get a setting value using dot notation.
        
        Args:
            key_path: Dot-separated path to setting (e.g., 'email.username')
            default: Default value if key not found
            
        Returns:
            Setting value or default
        """
        keys = key_path.split('.')
        value = self.settings
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path: str, value: Any):
        """Set a setting value using dot notation.
        
        Args:
            key_path: Dot-separated path to setting (e.g., 'email.username')
            value: Value to set
        """
        keys = key_path.split('.')
        current = self.settings
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
            
        # Set the value
        current[keys[-1]] = value
        
        # Auto-save if enabled
        if self.get('app.auto_save', True):
            self.save()
            
    def save(self):
        """Save current settings to file."""
        self._save_settings(self.settings)
        
    def reset_to_defaults(self):
        """Reset all settings to defaults."""
        import copy
        self.settings = copy.deepcopy(self._default_settings)
        self.save()
        self.logger.info("Settings reset to defaults")
        
    def create_directories(self):
        """Create necessary directories based on settings."""
        directories = [
            self.get('email.download_folder'),
            self.get('files.temp_folder'),
            self.get('files.processed_folder'),
            self.get('youtube.upload_folder')
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.info(f"Created directory: {directory}")
                
    def validate_settings(self) -> bool:
        """Validate current settings.
        
        Returns:
            True if settings are valid, False otherwise
        """
        errors = []
        
        # Check required API keys (will be implemented in later stages)
        # if not self.get('deepseek.api_key'):
        #     errors.append("DeepSeek API key is required")
            
        # if not self.get('youtube.client_id') or not self.get('youtube.client_secret'):
        #     errors.append("YouTube API credentials are required")
            
        # Check directory permissions
        try:
            self.create_directories()
        except Exception as e:
            errors.append(f"Cannot create directories: {e}")
            
        if errors:
            for error in errors:
                self.logger.warning(f"Settings validation: {error}")
            return False
            
        return True
