# Desktop Assistant Requirements
# Core dependencies for the application

# HTTP requests and API calls
requests>=2.31.0

# Google APIs for YouTube integration
google-api-python-client>=2.100.0
google-auth-httplib2>=0.1.1
google-auth-oauthlib>=1.1.0

# GUI framework (alternative to tkinter)
# PyQt5>=5.15.9
# PyQt6>=6.5.0

# Application packaging
PyInstaller>=5.13.0

# Development and testing dependencies
pytest>=7.4.0
pytest-cov>=4.1.0

# Additional utilities
python-dotenv>=1.0.0
Pillow>=10.0.0

# Email handling
imapclient>=2.3.1
email-validator>=2.0.0

# File handling and compression
zipfile36>=0.1.3
tqdm>=4.66.0
magic-python>=0.4.27

# Async support (for future enhancements)
# aiohttp>=3.8.5
# asyncio

# Logging enhancements
# colorlog>=6.7.0

# Configuration management
# configparser (built-in)
# json (built-in)

# Note: Some packages are commented out as they may not be needed
# in the initial implementation. Uncomment as needed during development.
