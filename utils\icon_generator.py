"""
Icon Generator for Desktop Assistant

This module creates application icons programmatically.
"""

import os
from pathlib import Path
from typing import <PERSON><PERSON>

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def create_app_icon(size: int = 64, output_path: str = None) -> str:
    """Create application icon.
    
    Args:
        size: Icon size in pixels
        output_path: Output file path (optional)
        
    Returns:
        Path to created icon file
    """
    if not PIL_AVAILABLE:
        # Create a simple text-based icon fallback
        return create_text_icon(size, output_path)
        
    # Create icon with PIL
    return create_pil_icon(size, output_path)

def create_pil_icon(size: int = 64, output_path: str = None) -> str:
    """Create icon using PIL.
    
    Args:
        size: Icon size in pixels
        output_path: Output file path
        
    Returns:
        Path to created icon file
    """
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Define colors
    bg_color = (45, 85, 125, 255)  # Blue background
    accent_color = (255, 165, 0, 255)  # Orange accent
    text_color = (255, 255, 255, 255)  # White text
    
    # Draw background circle
    margin = size // 8
    draw.ellipse([margin, margin, size - margin, size - margin], fill=bg_color)
    
    # Draw accent elements (representing automation/workflow)
    center = size // 2
    
    # Draw gear-like elements
    gear_radius = size // 4
    for i in range(8):
        angle = i * 45
        x1 = center + int(gear_radius * 0.8 * (1 if i % 2 == 0 else 0.6))
        y1 = center
        # Rotate point around center
        import math
        rad = math.radians(angle)
        x = center + (x1 - center) * math.cos(rad) - (y1 - center) * math.sin(rad)
        y = center + (x1 - center) * math.sin(rad) + (y1 - center) * math.cos(rad)
        
        draw.ellipse([x - 2, y - 2, x + 2, y + 2], fill=accent_color)
    
    # Draw center circle
    center_radius = size // 8
    draw.ellipse([center - center_radius, center - center_radius, 
                  center + center_radius, center + center_radius], fill=accent_color)
    
    # Add "DA" text in center
    try:
        # Try to use a font
        font_size = size // 6
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "DA"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = center - text_width // 2
    text_y = center - text_height // 2
    
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    # Save icon
    if output_path is None:
        assets_dir = Path(__file__).parent.parent / "assets"
        assets_dir.mkdir(exist_ok=True)
        output_path = assets_dir / "icon.png"
    
    img.save(output_path, "PNG")
    
    # Also create ICO file for Windows
    ico_path = str(output_path).replace('.png', '.ico')
    img.save(ico_path, "ICO", sizes=[(16, 16), (32, 32), (48, 48), (64, 64)])
    
    return str(output_path)

def create_text_icon(size: int = 64, output_path: str = None) -> str:
    """Create a simple text-based icon (fallback when PIL not available).
    
    Args:
        size: Icon size (not used in text version)
        output_path: Output file path
        
    Returns:
        Path to created icon file (empty string if PIL not available)
    """
    # Without PIL, we can't create actual image files
    # Return empty string to indicate no icon available
    return ""

def get_icon_path() -> str:
    """Get path to application icon.
    
    Returns:
        Path to icon file, or empty string if not available
    """
    assets_dir = Path(__file__).parent.parent / "assets"
    
    # Check for existing icon files
    for ext in ['.ico', '.png']:
        icon_path = assets_dir / f"icon{ext}"
        if icon_path.exists():
            return str(icon_path)
    
    # Try to create icon
    try:
        return create_app_icon()
    except Exception:
        return ""

def setup_app_icon():
    """Set up application icon if it doesn't exist.
    
    Returns:
        Path to icon file or empty string
    """
    icon_path = get_icon_path()
    
    if not icon_path:
        # Try to create icon
        try:
            icon_path = create_app_icon()
        except Exception as e:
            print(f"Could not create application icon: {e}")
            return ""
    
    return icon_path
