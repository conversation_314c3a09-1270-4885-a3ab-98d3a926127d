"""
Settings Dialog for Desktop Assistant

This module contains the settings configuration dialog.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from typing import Dict, Any, Optional
from pathlib import Path

from config.settings import Settings
from config.api_keys import api_keys
from utils.error_handler import handle_exceptions

class SettingsDialog:
    """Settings configuration dialog."""
    
    def __init__(self, parent, settings: Settings):
        """Initialize settings dialog.
        
        Args:
            parent: Parent window
            settings: Settings instance
        """
        self.parent = parent
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Desktop Assistant Settings")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self._center_dialog()
        
        # Variables for form fields
        self._create_variables()
        
        # Setup UI
        self._setup_ui()
        
        # Load current settings
        self._load_settings()
        
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate center position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
        
    def _create_variables(self):
        """Create tkinter variables for form fields."""
        # Email settings
        self.email_server = tk.StringVar()
        self.email_port = tk.StringVar()
        self.email_username = tk.StringVar()
        self.email_password = tk.StringVar()
        self.email_use_ssl = tk.BooleanVar()
        self.email_download_folder = tk.StringVar()
        self.email_max_emails = tk.StringVar()
        
        # API keys
        self.deepseek_api_key = tk.StringVar()
        self.youtube_client_id = tk.StringVar()
        self.youtube_client_secret = tk.StringVar()
        
        # File settings
        self.temp_folder = tk.StringVar()
        self.processed_folder = tk.StringVar()
        self.max_file_size = tk.StringVar()
        
        # App settings
        self.log_level = tk.StringVar()
        self.auto_save = tk.BooleanVar()
        
    def _setup_ui(self):
        """Set up the user interface."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Email tab
        self._create_email_tab(notebook)
        
        # API Keys tab
        self._create_api_tab(notebook)
        
        # File Settings tab
        self._create_file_tab(notebook)
        
        # Application tab
        self._create_app_tab(notebook)
        
        # Buttons frame
        self._create_buttons()
        
    def _create_email_tab(self, notebook):
        """Create email settings tab."""
        email_frame = ttk.Frame(notebook)
        notebook.add(email_frame, text="Email Settings")
        
        # Main frame with padding
        main_frame = ttk.Frame(email_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Email server settings
        server_frame = ttk.LabelFrame(main_frame, text="Email Server", padding="10")
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(server_frame, text="Server:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.email_server, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        ttk.Label(server_frame, text="Port:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.email_port, width=10).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(10, 0))
        
        ttk.Label(server_frame, text="Username:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.email_username, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))

        ttk.Label(server_frame, text="Password:").grid(row=3, column=0, sticky=tk.W, pady=2)
        password_entry = ttk.Entry(server_frame, textvariable=self.email_password, width=40, show="*")
        password_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        ttk.Button(server_frame, text="Show", command=lambda: self._toggle_password(password_entry)).grid(row=3, column=2, pady=2, padx=(5, 0))

        ttk.Checkbutton(server_frame, text="Use SSL", variable=self.email_use_ssl).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)

        ttk.Label(server_frame, text="Max Emails:").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.email_max_emails, width=10).grid(row=5, column=1, sticky=tk.W, pady=2, padx=(10, 0))
        
        server_frame.columnconfigure(1, weight=1)
        
        # Download settings
        download_frame = ttk.LabelFrame(main_frame, text="Download Settings", padding="10")
        download_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(download_frame, text="Download Folder:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(download_frame, textvariable=self.email_download_folder, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        ttk.Button(download_frame, text="Browse", command=lambda: self._browse_folder(self.email_download_folder)).grid(row=0, column=2, pady=2, padx=(5, 0))
        
        download_frame.columnconfigure(1, weight=1)
        
    def _create_api_tab(self, notebook):
        """Create API keys tab."""
        api_frame = ttk.Frame(notebook)
        notebook.add(api_frame, text="API Keys")
        
        main_frame = ttk.Frame(api_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # DeepSeek API
        deepseek_frame = ttk.LabelFrame(main_frame, text="DeepSeek API", padding="10")
        deepseek_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(deepseek_frame, text="API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        api_entry = ttk.Entry(deepseek_frame, textvariable=self.deepseek_api_key, width=50, show="*")
        api_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        ttk.Button(deepseek_frame, text="Show", command=lambda: self._toggle_password(api_entry)).grid(row=0, column=2, pady=2, padx=(5, 0))
        
        deepseek_frame.columnconfigure(1, weight=1)
        
        # YouTube API
        youtube_frame = ttk.LabelFrame(main_frame, text="YouTube API", padding="10")
        youtube_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(youtube_frame, text="Client ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(youtube_frame, textvariable=self.youtube_client_id, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        ttk.Label(youtube_frame, text="Client Secret:").grid(row=1, column=0, sticky=tk.W, pady=2)
        secret_entry = ttk.Entry(youtube_frame, textvariable=self.youtube_client_secret, width=50, show="*")
        secret_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        ttk.Button(youtube_frame, text="Show", command=lambda: self._toggle_password(secret_entry)).grid(row=1, column=2, pady=2, padx=(5, 0))
        
        youtube_frame.columnconfigure(1, weight=1)
        
    def _create_file_tab(self, notebook):
        """Create file settings tab."""
        file_frame = ttk.Frame(notebook)
        notebook.add(file_frame, text="File Settings")
        
        main_frame = ttk.Frame(file_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Folder settings
        folder_frame = ttk.LabelFrame(main_frame, text="Folder Locations", padding="10")
        folder_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(folder_frame, text="Temp Folder:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(folder_frame, textvariable=self.temp_folder, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        ttk.Button(folder_frame, text="Browse", command=lambda: self._browse_folder(self.temp_folder)).grid(row=0, column=2, pady=2, padx=(5, 0))
        
        ttk.Label(folder_frame, text="Processed Folder:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(folder_frame, textvariable=self.processed_folder, width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        ttk.Button(folder_frame, text="Browse", command=lambda: self._browse_folder(self.processed_folder)).grid(row=1, column=2, pady=2, padx=(5, 0))
        
        folder_frame.columnconfigure(1, weight=1)
        
        # File limits
        limits_frame = ttk.LabelFrame(main_frame, text="File Limits", padding="10")
        limits_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(limits_frame, text="Max File Size (MB):").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(limits_frame, textvariable=self.max_file_size, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(10, 0))
        
    def _create_app_tab(self, notebook):
        """Create application settings tab."""
        app_frame = ttk.Frame(notebook)
        notebook.add(app_frame, text="Application")
        
        main_frame = ttk.Frame(app_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # General settings
        general_frame = ttk.LabelFrame(main_frame, text="General Settings", padding="10")
        general_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(general_frame, text="Log Level:").grid(row=0, column=0, sticky=tk.W, pady=2)
        log_combo = ttk.Combobox(general_frame, textvariable=self.log_level, values=["DEBUG", "INFO", "WARNING", "ERROR"], state="readonly")
        log_combo.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(10, 0))
        
        ttk.Checkbutton(general_frame, text="Auto-save settings", variable=self.auto_save).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        
    def _create_buttons(self):
        """Create dialog buttons."""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="Test Connection", command=self._test_connection).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Reset to Defaults", command=self._reset_defaults).pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Button(button_frame, text="Cancel", command=self._cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Apply", command=self._apply).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(button_frame, text="OK", command=self._ok).pack(side=tk.RIGHT, padx=(0, 10))
        
    def _browse_folder(self, var: tk.StringVar):
        """Browse for folder."""
        folder = filedialog.askdirectory(initialdir=var.get())
        if folder:
            var.set(folder)
            
    def _toggle_password(self, entry: ttk.Entry):
        """Toggle password visibility."""
        if entry.cget('show') == '*':
            entry.config(show='')
        else:
            entry.config(show='*')
            
    def _load_settings(self):
        """Load current settings into form."""
        # Email settings
        self.email_server.set(self.settings.get('email.server', ''))
        self.email_port.set(str(self.settings.get('email.port', 993)))
        self.email_username.set(self.settings.get('email.username', ''))
        self.email_password.set(api_keys.get_key('email_password') or '')
        self.email_use_ssl.set(self.settings.get('email.use_ssl', True))
        self.email_download_folder.set(self.settings.get('email.download_folder', ''))
        self.email_max_emails.set(str(self.settings.get('email.max_emails', 50)))
        
        # API keys
        self.deepseek_api_key.set(api_keys.get_key('deepseek') or '')
        self.youtube_client_id.set(api_keys.get_key('youtube_client_id') or '')
        self.youtube_client_secret.set(api_keys.get_key('youtube_client_secret') or '')
        
        # File settings
        self.temp_folder.set(self.settings.get('files.temp_folder', ''))
        self.processed_folder.set(self.settings.get('files.processed_folder', ''))
        self.max_file_size.set(str(self.settings.get('files.max_file_size_mb', 500)))
        
        # App settings
        self.log_level.set(self.settings.get('app.log_level', 'INFO'))
        self.auto_save.set(self.settings.get('app.auto_save', True))
        
    @handle_exceptions(context="settings save")
    def _save_settings(self):
        """Save settings from form."""
        # Email settings
        self.settings.set('email.server', self.email_server.get())
        self.settings.set('email.port', int(self.email_port.get()) if self.email_port.get().isdigit() else 993)
        self.settings.set('email.username', self.email_username.get())
        self.settings.set('email.use_ssl', self.email_use_ssl.get())
        self.settings.set('email.download_folder', self.email_download_folder.get())
        self.settings.set('email.max_emails', int(self.email_max_emails.get()) if self.email_max_emails.get().isdigit() else 50)

        # Save email password securely
        if self.email_password.get():
            api_keys.set_key('email_password', self.email_password.get())
        
        # API keys
        if self.deepseek_api_key.get():
            api_keys.set_key('deepseek', self.deepseek_api_key.get())
        if self.youtube_client_id.get():
            api_keys.set_key('youtube_client_id', self.youtube_client_id.get())
        if self.youtube_client_secret.get():
            api_keys.set_key('youtube_client_secret', self.youtube_client_secret.get())
            
        # File settings
        self.settings.set('files.temp_folder', self.temp_folder.get())
        self.settings.set('files.processed_folder', self.processed_folder.get())
        self.settings.set('files.max_file_size_mb', int(self.max_file_size.get()) if self.max_file_size.get().isdigit() else 500)
        
        # App settings
        self.settings.set('app.log_level', self.log_level.get())
        self.settings.set('app.auto_save', self.auto_save.get())
        
        # Create directories
        self.settings.create_directories()
        
        self.logger.info("Settings saved successfully")
        
    def _test_connection(self):
        """Test email connection."""
        try:
            # Import here to avoid dependency issues if not installed
            from modules.email_handler import EmailHandler

            # Create temporary settings for testing
            test_settings = self.settings
            test_settings.set('email.server', self.email_server.get())
            test_settings.set('email.port', int(self.email_port.get()) if self.email_port.get().isdigit() else 993)
            test_settings.set('email.username', self.email_username.get())
            test_settings.set('email.use_ssl', self.email_use_ssl.get())

            # Test connection
            email_handler = EmailHandler(test_settings)
            if email_handler.test_connection(self.email_password.get()):
                messagebox.showinfo("Connection Test", "✅ Email connection successful!")
            else:
                messagebox.showerror("Connection Test", "❌ Email connection failed. Please check your settings.")

        except Exception as e:
            messagebox.showerror("Connection Test", f"❌ Connection test failed: {str(e)}")
        
    def _reset_defaults(self):
        """Reset settings to defaults."""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            self.settings.reset_to_defaults()
            self._load_settings()
            messagebox.showinfo("Reset Complete", "Settings have been reset to defaults")
            
    def _ok(self):
        """OK button handler."""
        self._save_settings()
        self.dialog.destroy()
        
    def _apply(self):
        """Apply button handler."""
        self._save_settings()
        messagebox.showinfo("Settings Applied", "Settings have been saved successfully")
        
    def _cancel(self):
        """Cancel button handler."""
        self.dialog.destroy()
