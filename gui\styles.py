"""
GUI Styles for Desktop Assistant

This module contains styling and theming for the GUI components.
"""

import tkinter as tk
from tkinter import ttk
import logging

class AppStyles:
    """Application styling manager."""
    
    def __init__(self, root: tk.Tk):
        """Initialize styling.
        
        Args:
            root: Root tkinter window
        """
        self.root = root
        self.logger = logging.getLogger(__name__)
        self.style = ttk.Style()
        
        # Color scheme
        self.colors = {
            'primary': '#2D5A7D',      # Blue
            'secondary': '#FFA500',     # Orange
            'success': '#28A745',       # Green
            'warning': '#FFC107',       # Yellow
            'danger': '#DC3545',        # Red
            'light': '#F8F9FA',         # Light gray
            'dark': '#343A40',          # Dark gray
            'white': '#FFFFFF',
            'black': '#000000'
        }
        
        self._setup_styles()
        
    def _setup_styles(self):
        """Set up custom styles."""
        try:
            # Configure the style theme
            available_themes = self.style.theme_names()
            
            # Prefer modern themes
            preferred_themes = ['vista', 'xpnative', 'winnative', 'clam']
            selected_theme = 'default'
            
            for theme in preferred_themes:
                if theme in available_themes:
                    selected_theme = theme
                    break
                    
            self.style.theme_use(selected_theme)
            self.logger.info(f"Using theme: {selected_theme}")
            
            # Configure custom styles
            self._configure_button_styles()
            self._configure_frame_styles()
            self._configure_label_styles()
            self._configure_entry_styles()
            self._configure_progressbar_styles()
            
        except Exception as e:
            self.logger.error(f"Failed to setup styles: {e}")
            
    def _configure_button_styles(self):
        """Configure button styles."""
        # Primary button style
        self.style.configure(
            'Primary.TButton',
            background=self.colors['primary'],
            foreground=self.colors['white'],
            borderwidth=1,
            focuscolor='none',
            padding=(10, 5)
        )
        
        self.style.map(
            'Primary.TButton',
            background=[
                ('active', self._lighten_color(self.colors['primary'])),
                ('pressed', self._darken_color(self.colors['primary']))
            ]
        )
        
        # Accent button style (for main workflow button)
        self.style.configure(
            'Accent.TButton',
            background=self.colors['secondary'],
            foreground=self.colors['white'],
            borderwidth=1,
            focuscolor='none',
            padding=(10, 8),
            font=('Arial', 10, 'bold')
        )
        
        self.style.map(
            'Accent.TButton',
            background=[
                ('active', self._lighten_color(self.colors['secondary'])),
                ('pressed', self._darken_color(self.colors['secondary']))
            ]
        )
        
        # Success button style
        self.style.configure(
            'Success.TButton',
            background=self.colors['success'],
            foreground=self.colors['white'],
            borderwidth=1,
            focuscolor='none',
            padding=(10, 5)
        )
        
        # Warning button style
        self.style.configure(
            'Warning.TButton',
            background=self.colors['warning'],
            foreground=self.colors['dark'],
            borderwidth=1,
            focuscolor='none',
            padding=(10, 5)
        )
        
        # Danger button style
        self.style.configure(
            'Danger.TButton',
            background=self.colors['danger'],
            foreground=self.colors['white'],
            borderwidth=1,
            focuscolor='none',
            padding=(10, 5)
        )
        
    def _configure_frame_styles(self):
        """Configure frame styles."""
        # Main frame style
        self.style.configure(
            'Main.TFrame',
            background=self.colors['light'],
            relief='flat',
            borderwidth=0
        )
        
        # Card frame style
        self.style.configure(
            'Card.TFrame',
            background=self.colors['white'],
            relief='solid',
            borderwidth=1
        )
        
    def _configure_label_styles(self):
        """Configure label styles."""
        # Title label style
        self.style.configure(
            'Title.TLabel',
            background=self.colors['light'],
            foreground=self.colors['dark'],
            font=('Arial', 16, 'bold')
        )
        
        # Heading label style
        self.style.configure(
            'Heading.TLabel',
            background=self.colors['light'],
            foreground=self.colors['primary'],
            font=('Arial', 12, 'bold')
        )
        
        # Status label style
        self.style.configure(
            'Status.TLabel',
            background=self.colors['light'],
            foreground=self.colors['dark'],
            font=('Arial', 9)
        )
        
    def _configure_entry_styles(self):
        """Configure entry styles."""
        self.style.configure(
            'Custom.TEntry',
            fieldbackground=self.colors['white'],
            borderwidth=1,
            insertcolor=self.colors['primary']
        )
        
    def _configure_progressbar_styles(self):
        """Configure progress bar styles."""
        self.style.configure(
            'Custom.Horizontal.TProgressbar',
            background=self.colors['primary'],
            troughcolor=self.colors['light'],
            borderwidth=1,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )
        
    def _lighten_color(self, color: str, factor: float = 0.2) -> str:
        """Lighten a hex color.
        
        Args:
            color: Hex color string
            factor: Lightening factor (0-1)
            
        Returns:
            Lightened hex color
        """
        try:
            # Remove # if present
            color = color.lstrip('#')
            
            # Convert to RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            
            # Lighten
            r = min(255, int(r + (255 - r) * factor))
            g = min(255, int(g + (255 - g) * factor))
            b = min(255, int(b + (255 - b) * factor))
            
            # Convert back to hex
            return f'#{r:02x}{g:02x}{b:02x}'
            
        except Exception:
            return color
            
    def _darken_color(self, color: str, factor: float = 0.2) -> str:
        """Darken a hex color.
        
        Args:
            color: Hex color string
            factor: Darkening factor (0-1)
            
        Returns:
            Darkened hex color
        """
        try:
            # Remove # if present
            color = color.lstrip('#')
            
            # Convert to RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            
            # Darken
            r = max(0, int(r * (1 - factor)))
            g = max(0, int(g * (1 - factor)))
            b = max(0, int(b * (1 - factor)))
            
            # Convert back to hex
            return f'#{r:02x}{g:02x}{b:02x}'
            
        except Exception:
            return color
            
    def apply_window_style(self, window: tk.Tk):
        """Apply styling to a window.
        
        Args:
            window: Window to style
        """
        try:
            window.configure(bg=self.colors['light'])
        except Exception as e:
            self.logger.warning(f"Could not apply window style: {e}")
            
    def get_color(self, name: str) -> str:
        """Get a color by name.
        
        Args:
            name: Color name
            
        Returns:
            Hex color string
        """
        return self.colors.get(name, '#000000')

def setup_app_styling(root: tk.Tk) -> AppStyles:
    """Set up application styling.
    
    Args:
        root: Root tkinter window
        
    Returns:
        AppStyles instance
    """
    styles = AppStyles(root)
    styles.apply_window_style(root)
    return styles
