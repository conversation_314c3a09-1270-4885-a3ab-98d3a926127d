"""
Email Handler Module for Desktop Assistant

This module handles email operations including:
- Connecting to email servers (IMAP/Gmail)
- Searching for emails by sender and title
- Downloading attachments
- Email authentication and security

Implemented in Stage 3.
"""

import logging
import imaplib
import email
import os
import re
from typing import List, Dict, Optional, Tuple, Callable
from pathlib import Path
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

try:
    import imapclient
    from imapclient import IMAPClient
    IMAPCLIENT_AVAILABLE = True
except ImportError:
    IMAPCLIENT_AVAILABLE = False
    IMAPClient = None

try:
    import email_validator
    EMAIL_VALIDATOR_AVAILABLE = True
except ImportError:
    EMAIL_VALIDATOR_AVAILABLE = False

from utils.error_handler import EmailError, handle_exceptions
from config.settings import Settings
from config.api_keys import api_keys

class EmailHandler:
    """Handles email operations for the Desktop Assistant."""

    def __init__(self, settings: Settings):
        """Initialize email handler.

        Args:
            settings: Application settings instance
        """
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.connection = None
        self.server = None
        self.is_authenticated = False

        # Email server configurations
        self.server_configs = {
            'gmail.com': {'server': 'imap.gmail.com', 'port': 993, 'ssl': True},
            'outlook.com': {'server': 'outlook.office365.com', 'port': 993, 'ssl': True},
            'hotmail.com': {'server': 'outlook.office365.com', 'port': 993, 'ssl': True},
            'yahoo.com': {'server': 'imap.mail.yahoo.com', 'port': 993, 'ssl': True},
            'icloud.com': {'server': 'imap.mail.me.com', 'port': 993, 'ssl': True}
        }
        
    def _get_server_config(self, email_address: str) -> Dict[str, any]:
        """Get server configuration based on email address.

        Args:
            email_address: Email address to determine server config

        Returns:
            Server configuration dictionary
        """
        if '@' in email_address:
            domain = email_address.split('@')[1].lower()
            if domain in self.server_configs:
                return self.server_configs[domain]

        # Return manual configuration from settings
        return {
            'server': self.settings.get('email.server', ''),
            'port': self.settings.get('email.port', 993),
            'ssl': self.settings.get('email.use_ssl', True)
        }

    @handle_exceptions(context="email connection")
    def connect(self, password: Optional[str] = None) -> bool:
        """Connect to email server.

        Args:
            password: Email password (optional, will use stored password if not provided)

        Returns:
            True if connection successful, False otherwise

        Raises:
            EmailError: If connection fails
        """
        try:
            if self.is_connected():
                self.logger.info("Already connected to email server")
                return True

            username = self.settings.get('email.username', '')
            if not username:
                raise EmailError("Email username not configured")

            # Get password
            if not password:
                password = api_keys.get_key('email_password')
            if not password:
                raise EmailError("Email password not configured")

            # Validate email address
            if EMAIL_VALIDATOR_AVAILABLE:
                try:
                    email_validator.validate_email(username)
                except email_validator.EmailNotValidError as e:
                    raise EmailError(f"Invalid email address: {e}")

            # Get server configuration
            server_config = self._get_server_config(username)
            server_host = server_config.get('server')
            server_port = server_config.get('port', 993)
            use_ssl = server_config.get('ssl', True)

            if not server_host:
                raise EmailError("Email server not configured")

            self.logger.info(f"Connecting to {server_host}:{server_port} (SSL: {use_ssl})")

            # Use IMAPClient if available, otherwise fall back to imaplib
            if IMAPCLIENT_AVAILABLE:
                self.connection = IMAPClient(server_host, port=server_port, ssl=use_ssl)
                self.connection.login(username, password)
                self.server = 'imapclient'
            else:
                # Fallback to built-in imaplib
                if use_ssl:
                    self.connection = imaplib.IMAP4_SSL(server_host, server_port)
                else:
                    self.connection = imaplib.IMAP4(server_host, server_port)
                self.connection.login(username, password)
                self.server = 'imaplib'

            self.is_authenticated = True
            self.logger.info("Successfully connected to email server")
            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to email server: {e}")
            self.connection = None
            self.is_authenticated = False
            if "authentication failed" in str(e).lower():
                raise EmailError("Email authentication failed. Please check your username and password.")
            elif "connection" in str(e).lower():
                raise EmailError("Failed to connect to email server. Please check your server settings.")
            else:
                raise EmailError(f"Email connection failed: {e}")

    def test_connection(self, password: Optional[str] = None) -> bool:
        """Test email connection without storing the connection.

        Args:
            password: Email password to test

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Store current connection state
            old_connection = self.connection
            old_authenticated = self.is_authenticated

            # Test connection
            result = self.connect(password)

            # Disconnect test connection
            if self.connection:
                self.disconnect()

            # Restore previous state
            self.connection = old_connection
            self.is_authenticated = old_authenticated

            return result

        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
        
    @handle_exceptions(context="email search")
    def search_emails(
        self,
        sender: str = "",
        subject_contains: str = "",
        limit: int = None,
        days_back: int = 30,
        folder: str = "INBOX",
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[Dict]:
        """Search for emails by sender and subject.

        Args:
            sender: Email sender to search for (optional)
            subject_contains: Text that should be in subject (optional)
            limit: Maximum number of emails to return (optional)
            days_back: Number of days to search back
            folder: Email folder to search in
            progress_callback: Callback for progress updates (current, total)

        Returns:
            List of email dictionaries with keys: id, sender, subject, date, has_attachments

        Raises:
            EmailError: If search fails
        """
        try:
            if not self.is_connected():
                raise EmailError("Not connected to email server")

            if limit is None:
                limit = self.settings.get('email.max_emails', 50)

            self.logger.info(f"Searching emails in {folder} - sender: '{sender}', subject: '{subject_contains}', limit: {limit}")

            # Select folder
            if self.server == 'imapclient':
                self.connection.select_folder(folder)
            else:
                self.connection.select(folder)

            # Build search criteria
            search_criteria = []

            # Date criteria (search last N days)
            if days_back > 0:
                since_date = datetime.now() - timedelta(days=days_back)
                if self.server == 'imapclient':
                    search_criteria.append(['SINCE', since_date.date()])
                else:
                    date_str = since_date.strftime('%d-%b-%Y')
                    search_criteria.append(f'SINCE {date_str}')

            # Sender criteria
            if sender:
                if self.server == 'imapclient':
                    search_criteria.append(['FROM', sender])
                else:
                    search_criteria.append(f'FROM "{sender}"')

            # Subject criteria
            if subject_contains:
                if self.server == 'imapclient':
                    search_criteria.append(['SUBJECT', subject_contains])
                else:
                    search_criteria.append(f'SUBJECT "{subject_contains}"')

            # Perform search
            if self.server == 'imapclient':
                if search_criteria:
                    message_ids = self.connection.search(search_criteria)
                else:
                    message_ids = self.connection.search(['ALL'])
            else:
                if search_criteria:
                    search_string = ' '.join(search_criteria)
                else:
                    search_string = 'ALL'
                status, message_ids = self.connection.search(None, search_string)
                if status != 'OK':
                    raise EmailError(f"Email search failed: {status}")
                message_ids = message_ids[0].split() if message_ids[0] else []

            # Limit results
            if limit and len(message_ids) > limit:
                message_ids = message_ids[-limit:]  # Get most recent emails

            self.logger.info(f"Found {len(message_ids)} emails matching criteria")

            # Fetch email details
            emails = []
            total_emails = len(message_ids)

            for i, msg_id in enumerate(message_ids):
                try:
                    if progress_callback:
                        progress_callback(i + 1, total_emails)

                    email_data = self._fetch_email_details(msg_id)
                    if email_data:
                        emails.append(email_data)

                except Exception as e:
                    self.logger.warning(f"Failed to fetch email {msg_id}: {e}")
                    continue

            self.logger.info(f"Successfully retrieved {len(emails)} email details")
            return emails

        except Exception as e:
            self.logger.error(f"Email search failed: {e}")
            raise EmailError(f"Email search failed: {e}")

    def _fetch_email_details(self, message_id) -> Optional[Dict]:
        """Fetch details for a specific email.

        Args:
            message_id: Email message ID

        Returns:
            Email details dictionary or None if failed
        """
        try:
            if self.server == 'imapclient':
                # Fetch email data using IMAPClient
                response = self.connection.fetch([message_id], ['ENVELOPE', 'BODYSTRUCTURE'])
                envelope = response[message_id][b'ENVELOPE']
                bodystructure = response[message_id][b'BODYSTRUCTURE']

                # Extract details
                sender = str(envelope.from_[0]) if envelope.from_ else "Unknown"
                subject = envelope.subject.decode() if envelope.subject else "No Subject"
                date = envelope.date

                # Check for attachments
                has_attachments = self._has_attachments_imapclient(bodystructure)

            else:
                # Fetch email data using imaplib
                status, data = self.connection.fetch(message_id, '(RFC822.HEADER)')
                if status != 'OK':
                    return None

                # Parse email headers
                email_message = email.message_from_bytes(data[0][1])
                sender = email_message.get('From', 'Unknown')
                subject = email_message.get('Subject', 'No Subject')
                date_str = email_message.get('Date', '')

                # Parse date
                try:
                    date = email.utils.parsedate_to_datetime(date_str)
                except:
                    date = datetime.now()

                # Check for attachments (need to fetch full message)
                has_attachments = self._has_attachments_imaplib(message_id)

            return {
                'id': str(message_id),
                'sender': sender,
                'subject': subject,
                'date': date,
                'has_attachments': has_attachments,
                'folder': self.connection.folder if hasattr(self.connection, 'folder') else 'INBOX'
            }

        except Exception as e:
            self.logger.error(f"Failed to fetch email details for {message_id}: {e}")
            return None
        
    def _has_attachments_imapclient(self, bodystructure) -> bool:
        """Check if email has attachments using IMAPClient bodystructure.

        Args:
            bodystructure: Email bodystructure from IMAPClient

        Returns:
            True if email has attachments
        """
        try:
            # Recursively check bodystructure for attachments
            def check_part(part):
                if isinstance(part, list):
                    for subpart in part:
                        if check_part(subpart):
                            return True
                elif hasattr(part, 'disposition') and part.disposition:
                    if part.disposition.lower() == 'attachment':
                        return True
                return False

            return check_part(bodystructure)
        except:
            return False

    def _has_attachments_imaplib(self, message_id) -> bool:
        """Check if email has attachments using imaplib.

        Args:
            message_id: Email message ID

        Returns:
            True if email has attachments
        """
        try:
            status, data = self.connection.fetch(message_id, '(RFC822)')
            if status != 'OK':
                return False

            email_message = email.message_from_bytes(data[0][1])

            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    return True
            return False
        except:
            return False

    @handle_exceptions(context="attachment download")
    def download_attachments(
        self,
        email_id: str,
        download_folder: Optional[str] = None,
        file_types: Optional[List[str]] = None,
        progress_callback: Optional[Callable[[str, int, int], None]] = None
    ) -> List[str]:
        """Download attachments from an email.

        Args:
            email_id: Email identifier
            download_folder: Folder to download to (optional)
            file_types: List of file extensions to download (optional)
            progress_callback: Callback for progress updates (filename, current, total)

        Returns:
            List of downloaded file paths

        Raises:
            EmailError: If download fails
        """
        try:
            if not self.is_connected():
                raise EmailError("Not connected to email server")

            if download_folder is None:
                download_folder = self.settings.get('email.download_folder')

            if file_types is None:
                file_types = self.settings.get('email.attachment_types', ['.mp4', '.avi', '.mov'])

            # Ensure download folder exists
            download_path = Path(download_folder)
            download_path.mkdir(parents=True, exist_ok=True)

            self.logger.info(f"Downloading attachments from email {email_id} to {download_folder}")

            downloaded_files = []

            if self.server == 'imapclient':
                downloaded_files = self._download_attachments_imapclient(
                    email_id, download_path, file_types, progress_callback
                )
            else:
                downloaded_files = self._download_attachments_imaplib(
                    email_id, download_path, file_types, progress_callback
                )

            self.logger.info(f"Downloaded {len(downloaded_files)} attachments")
            return downloaded_files

        except Exception as e:
            self.logger.error(f"Failed to download attachments: {e}")
            raise EmailError(f"Attachment download failed: {e}")

    def _download_attachments_imapclient(
        self,
        email_id: str,
        download_path: Path,
        file_types: List[str],
        progress_callback: Optional[Callable[[str, int, int], None]]
    ) -> List[str]:
        """Download attachments using IMAPClient.

        Args:
            email_id: Email ID
            download_path: Download directory path
            file_types: Allowed file extensions
            progress_callback: Progress callback

        Returns:
            List of downloaded file paths
        """
        downloaded_files = []

        try:
            # Fetch email message
            response = self.connection.fetch([email_id], ['RFC822'])
            email_data = response[int(email_id)][b'RFC822']
            email_message = email.message_from_bytes(email_data)

            # Find attachments
            attachments = []
            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename and any(filename.lower().endswith(ext.lower()) for ext in file_types):
                        attachments.append((filename, part))

            total_attachments = len(attachments)

            for i, (filename, part) in enumerate(attachments):
                try:
                    if progress_callback:
                        progress_callback(filename, i + 1, total_attachments)

                    # Generate unique filename if file exists
                    file_path = download_path / filename
                    counter = 1
                    while file_path.exists():
                        name, ext = os.path.splitext(filename)
                        file_path = download_path / f"{name}_{counter}{ext}"
                        counter += 1

                    # Download attachment
                    with open(file_path, 'wb') as f:
                        f.write(part.get_payload(decode=True))

                    downloaded_files.append(str(file_path))
                    self.logger.info(f"Downloaded attachment: {file_path}")

                except Exception as e:
                    self.logger.error(f"Failed to download attachment {filename}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Failed to process email for attachments: {e}")

        return downloaded_files

    def _download_attachments_imaplib(
        self,
        email_id: str,
        download_path: Path,
        file_types: List[str],
        progress_callback: Optional[Callable[[str, int, int], None]]
    ) -> List[str]:
        """Download attachments using imaplib.

        Args:
            email_id: Email ID
            download_path: Download directory path
            file_types: Allowed file extensions
            progress_callback: Progress callback

        Returns:
            List of downloaded file paths
        """
        downloaded_files = []

        try:
            # Fetch email message
            status, data = self.connection.fetch(email_id, '(RFC822)')
            if status != 'OK':
                raise EmailError(f"Failed to fetch email {email_id}")

            email_message = email.message_from_bytes(data[0][1])

            # Find attachments
            attachments = []
            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename and any(filename.lower().endswith(ext.lower()) for ext in file_types):
                        attachments.append((filename, part))

            total_attachments = len(attachments)

            for i, (filename, part) in enumerate(attachments):
                try:
                    if progress_callback:
                        progress_callback(filename, i + 1, total_attachments)

                    # Generate unique filename if file exists
                    file_path = download_path / filename
                    counter = 1
                    while file_path.exists():
                        name, ext = os.path.splitext(filename)
                        file_path = download_path / f"{name}_{counter}{ext}"
                        counter += 1

                    # Download attachment
                    with open(file_path, 'wb') as f:
                        f.write(part.get_payload(decode=True))

                    downloaded_files.append(str(file_path))
                    self.logger.info(f"Downloaded attachment: {file_path}")

                except Exception as e:
                    self.logger.error(f"Failed to download attachment {filename}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Failed to process email for attachments: {e}")

        return downloaded_files
        
    def disconnect(self):
        """Disconnect from email server."""
        try:
            if self.connection:
                if self.server == 'imapclient':
                    self.connection.logout()
                else:
                    self.connection.close()
                    self.connection.logout()
                self.logger.info("Disconnected from email server")
        except Exception as e:
            self.logger.warning(f"Error during disconnect: {e}")
        finally:
            self.connection = None
            self.is_authenticated = False

    def is_connected(self) -> bool:
        """Check if connected to email server.

        Returns:
            True if connected, False otherwise
        """
        return self.connection is not None and self.is_authenticated

    def get_email_count(self, folder: str = "INBOX") -> int:
        """Get count of emails in folder.

        Args:
            folder: Email folder name

        Returns:
            Number of emails
        """
        try:
            if not self.is_connected():
                return 0

            if self.server == 'imapclient':
                self.connection.select_folder(folder)
                return len(self.connection.search(['ALL']))
            else:
                status, data = self.connection.select(folder)
                if status == 'OK':
                    return int(data[0])
                return 0

        except Exception as e:
            self.logger.error(f"Failed to get email count: {e}")
            return 0

    def get_folders(self) -> List[str]:
        """Get list of available email folders.

        Returns:
            List of folder names
        """
        try:
            if not self.is_connected():
                return ['INBOX']

            if self.server == 'imapclient':
                folders = self.connection.list_folders()
                return [folder[2] for folder in folders]
            else:
                status, folders = self.connection.list()
                if status == 'OK':
                    folder_names = []
                    for folder in folders:
                        # Parse folder name from IMAP response
                        parts = folder.decode().split('"')
                        if len(parts) >= 3:
                            folder_names.append(parts[-2])
                    return folder_names
                return ['INBOX']

        except Exception as e:
            self.logger.error(f"Failed to get folders: {e}")
            return ['INBOX']

    def mark_as_read(self, email_id: str) -> bool:
        """Mark an email as read.

        Args:
            email_id: Email identifier

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.is_connected():
                return False

            if self.server == 'imapclient':
                self.connection.add_flags([email_id], ['\\Seen'])
            else:
                self.connection.store(email_id, '+FLAGS', '\\Seen')

            return True

        except Exception as e:
            self.logger.error(f"Failed to mark email as read: {e}")
            return False

    def get_email_content(self, email_id: str) -> Optional[Dict]:
        """Get full email content including body.

        Args:
            email_id: Email identifier

        Returns:
            Dictionary with email content or None if failed
        """
        try:
            if not self.is_connected():
                return None

            if self.server == 'imapclient':
                response = self.connection.fetch([email_id], ['RFC822'])
                email_data = response[int(email_id)][b'RFC822']
            else:
                status, data = self.connection.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    return None
                email_data = data[0][1]

            email_message = email.message_from_bytes(email_data)

            # Extract text content
            text_content = ""
            html_content = ""

            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        text_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        html_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                content_type = email_message.get_content_type()
                if content_type == "text/plain":
                    text_content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    html_content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')

            return {
                'id': email_id,
                'sender': email_message.get('From', 'Unknown'),
                'subject': email_message.get('Subject', 'No Subject'),
                'date': email_message.get('Date', ''),
                'text_content': text_content,
                'html_content': html_content,
                'has_attachments': self._has_attachments_imaplib(email_id) if self.server == 'imaplib' else False
            }

        except Exception as e:
            self.logger.error(f"Failed to get email content: {e}")
            return None

    def search_emails_with_attachments(
        self,
        sender: str = "",
        subject_contains: str = "",
        file_types: Optional[List[str]] = None,
        limit: int = None,
        days_back: int = 30
    ) -> List[Dict]:
        """Search for emails that have video attachments.

        Args:
            sender: Email sender to search for
            subject_contains: Text that should be in subject
            file_types: List of file extensions to look for
            limit: Maximum number of emails to return
            days_back: Number of days to search back

        Returns:
            List of emails with video attachments
        """
        try:
            if file_types is None:
                file_types = self.settings.get('email.attachment_types', ['.mp4', '.avi', '.mov'])

            # Search for emails
            all_emails = self.search_emails(
                sender=sender,
                subject_contains=subject_contains,
                limit=limit * 2 if limit else None,  # Search more to filter for attachments
                days_back=days_back
            )

            # Filter emails with relevant attachments
            emails_with_attachments = []
            for email_data in all_emails:
                if email_data.get('has_attachments', False):
                    # Check if attachments are of the right type
                    if self._has_relevant_attachments(email_data['id'], file_types):
                        emails_with_attachments.append(email_data)

                if limit and len(emails_with_attachments) >= limit:
                    break

            return emails_with_attachments

        except Exception as e:
            self.logger.error(f"Failed to search emails with attachments: {e}")
            return []

    def _has_relevant_attachments(self, email_id: str, file_types: List[str]) -> bool:
        """Check if email has attachments of specified types.

        Args:
            email_id: Email identifier
            file_types: List of file extensions to check for

        Returns:
            True if email has relevant attachments
        """
        try:
            if self.server == 'imapclient':
                response = self.connection.fetch([email_id], ['RFC822'])
                email_data = response[int(email_id)][b'RFC822']
            else:
                status, data = self.connection.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    return False
                email_data = data[0][1]

            email_message = email.message_from_bytes(email_data)

            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename and any(filename.lower().endswith(ext.lower()) for ext in file_types):
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Failed to check attachments for email {email_id}: {e}")
            return False
