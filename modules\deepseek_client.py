"""
DeepSeek Client Module for Desktop Assistant

This module handles DeepSeek API integration including:
- API authentication and connection management
- Content analysis and metadata extraction
- AI-powered title generation
- Description generation for YouTube
- Smart file renaming suggestions
- Search functionality

Implemented in Stage 5.
"""

import logging
import json
import time
import re
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from pathlib import Path

try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

from utils.error_handler import DeepSeekError, handle_exceptions, validate_api_key
from config.settings import Settings
from config.api_keys import api_keys

class DeepSeekClient:
    """Handles DeepSeek API operations for the Desktop Assistant."""

    def __init__(self, settings: Settings):
        """Initialize DeepSeek client.

        Args:
            settings: Application settings instance
        """
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.api_key = None
        self.client = None
        self.is_authenticated = False

        # API configuration
        self.base_url = settings.get('deepseek.base_url', 'https://api.deepseek.com')
        self.model = settings.get('deepseek.model', 'deepseek-chat')
        self.max_tokens = settings.get('deepseek.max_tokens', 2000)
        self.temperature = settings.get('deepseek.temperature', 0.7)
        self.timeout = settings.get('deepseek.timeout', 30)
        self.retry_attempts = settings.get('deepseek.retry_attempts', 3)

        # Prompt templates
        self.title_prompt = settings.get('deepseek.title_prompt',
            "Generate a concise, descriptive title for this content. Focus on the main topic and make it engaging for viewers.")
        self.description_prompt = settings.get('deepseek.description_prompt',
            "Create a detailed description for this video content. Include key points, context, and make it informative for viewers.")
        self.analysis_prompt = settings.get('deepseek.analysis_prompt',
            "Analyze this content and extract key information including main topics, themes, and important details.")

        # Usage tracking
        self.usage_stats = {
            "requests_made": 0,
            "tokens_used": 0,
            "errors": 0,
            "last_request": None
        }
        
    @handle_exceptions(context="DeepSeek authentication")
    def authenticate(self, api_key: Optional[str] = None) -> bool:
        """Authenticate with DeepSeek API.

        Args:
            api_key: API key to use (optional, will use stored key if not provided)

        Returns:
            True if authentication successful, False otherwise

        Raises:
            DeepSeekError: If authentication fails
        """
        try:
            # Get API key
            if not api_key:
                api_key = api_keys.get_key('deepseek')
            if not api_key:
                raise DeepSeekError("DeepSeek API key not configured")

            # Validate API key format
            validate_api_key(api_key, 'deepseek')

            self.api_key = api_key

            # Initialize OpenAI client with DeepSeek endpoint
            if OPENAI_AVAILABLE:
                self.client = OpenAI(
                    api_key=api_key,
                    base_url=self.base_url,
                    timeout=self.timeout
                )
            else:
                # Fallback to requests if OpenAI library not available
                self.client = None

            # Test authentication with a simple request
            test_result = self._test_connection()

            if test_result:
                self.is_authenticated = True
                self.logger.info("Successfully authenticated with DeepSeek API")
                return True
            else:
                raise DeepSeekError("Authentication test failed")

        except Exception as e:
            self.logger.error(f"DeepSeek authentication failed: {e}")
            self.is_authenticated = False
            self.client = None
            if "api key" in str(e).lower():
                raise DeepSeekError("Invalid API key. Please check your DeepSeek API key.")
            else:
                raise DeepSeekError(f"Authentication failed: {e}")

    def _test_connection(self) -> bool:
        """Test the API connection with a simple request.

        Returns:
            True if connection successful
        """
        try:
            # Simple test prompt
            test_prompt = "Say 'Hello' in one word."

            response = self._make_api_request(
                messages=[{"role": "user", "content": test_prompt}],
                max_tokens=10
            )

            return response is not None and len(response.strip()) > 0

        except Exception as e:
            self.logger.debug(f"Connection test failed: {e}")
            return False

    def _make_api_request(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        retry_count: int = 0
    ) -> str:
        """Make an API request to DeepSeek.

        Args:
            messages: List of message dictionaries
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            retry_count: Current retry attempt

        Returns:
            Generated text response

        Raises:
            DeepSeekError: If request fails
        """
        try:
            if not self.is_authenticated:
                raise DeepSeekError("Not authenticated with DeepSeek API")

            # Use provided values or defaults
            max_tokens = max_tokens or self.max_tokens
            temperature = temperature or self.temperature

            # Track request
            self.usage_stats["requests_made"] += 1
            self.usage_stats["last_request"] = datetime.now()

            if OPENAI_AVAILABLE and self.client:
                # Use OpenAI client
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    timeout=self.timeout
                )

                # Extract response text
                if response.choices and len(response.choices) > 0:
                    content = response.choices[0].message.content

                    # Track token usage
                    if hasattr(response, 'usage') and response.usage:
                        self.usage_stats["tokens_used"] += response.usage.total_tokens

                    return content.strip() if content else ""
                else:
                    raise DeepSeekError("Empty response from API")

            else:
                # Fallback to direct HTTP requests
                return self._make_http_request(messages, max_tokens, temperature)

        except Exception as e:
            self.usage_stats["errors"] += 1

            # Retry logic
            if retry_count < self.retry_attempts and "rate limit" in str(e).lower():
                self.logger.warning(f"Rate limited, retrying in {2 ** retry_count} seconds...")
                time.sleep(2 ** retry_count)
                return self._make_api_request(messages, max_tokens, temperature, retry_count + 1)

            self.logger.error(f"API request failed: {e}")
            raise DeepSeekError(f"API request failed: {e}")

    def _make_http_request(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int,
        temperature: float
    ) -> str:
        """Make HTTP request to DeepSeek API (fallback method).

        Args:
            messages: List of message dictionaries
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature

        Returns:
            Generated text response
        """
        if not REQUESTS_AVAILABLE:
            raise DeepSeekError("Neither OpenAI library nor requests library available")

        try:
            url = f"{self.base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature
            }

            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()

            result = response.json()

            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]

                # Track token usage
                if "usage" in result:
                    self.usage_stats["tokens_used"] += result["usage"]["total_tokens"]

                return content.strip() if content else ""
            else:
                raise DeepSeekError("Invalid response format from API")

        except requests.RequestException as e:
            raise DeepSeekError(f"HTTP request failed: {e}")
        except json.JSONDecodeError as e:
            raise DeepSeekError(f"Invalid JSON response: {e}")
        
    @handle_exceptions(context="content analysis")
    def analyze_content(
        self,
        content: str,
        content_type: str = "text",
        extract_keywords: bool = True
    ) -> Dict[str, Any]:
        """Analyze content and extract metadata using AI.

        Args:
            content: Content to analyze (text, filename, etc.)
            content_type: Type of content (text, filename, description)
            extract_keywords: Whether to extract keywords

        Returns:
            Dictionary with analysis results

        Raises:
            DeepSeekError: If analysis fails
        """
        try:
            if not self.is_authenticated:
                raise DeepSeekError("Not authenticated with DeepSeek API")

            if not content or not content.strip():
                raise DeepSeekError("No content provided for analysis")

            self.logger.info(f"Analyzing {content_type} content: {content[:100]}...")

            # Prepare analysis prompt
            analysis_prompt = self._build_analysis_prompt(content, content_type, extract_keywords)

            messages = [
                {"role": "system", "content": "You are an AI assistant specialized in content analysis. Provide structured, accurate analysis."},
                {"role": "user", "content": analysis_prompt}
            ]

            # Make API request
            response = self._make_api_request(messages, max_tokens=1000)

            # Parse response
            analysis_result = self._parse_analysis_response(response)

            # Add metadata
            analysis_result.update({
                "original_content": content,
                "content_type": content_type,
                "analyzed_at": datetime.now().isoformat(),
                "model_used": self.model
            })

            self.logger.info("Content analysis completed successfully")
            return analysis_result

        except Exception as e:
            self.logger.error(f"Content analysis failed: {e}")
            raise DeepSeekError(f"Content analysis failed: {e}")

    def _build_analysis_prompt(self, content: str, content_type: str, extract_keywords: bool) -> str:
        """Build analysis prompt based on content type.

        Args:
            content: Content to analyze
            content_type: Type of content
            extract_keywords: Whether to extract keywords

        Returns:
            Formatted prompt string
        """
        base_prompt = self.analysis_prompt

        if content_type == "filename":
            specific_prompt = f"""
Analyze this filename and extract meaningful information:
"{content}"

Please provide:
1. Main topic or subject
2. Likely content type or category
3. Any dates, numbers, or identifiers
4. Suggested improvements for the filename
"""
        elif content_type == "description":
            specific_prompt = f"""
Analyze this content description:
"{content}"

Please provide:
1. Main topics and themes
2. Key points and highlights
3. Target audience
4. Content category or genre
"""
        else:
            specific_prompt = f"""
Analyze this content:
"{content}"

Please provide:
1. Main topics and themes
2. Key information and highlights
3. Content summary
4. Relevant categories or tags
"""

        if extract_keywords:
            specific_prompt += "\n5. Extract 5-10 relevant keywords or tags"

        specific_prompt += "\n\nProvide your analysis in a clear, structured format."

        return specific_prompt

    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse AI analysis response into structured data.

        Args:
            response: Raw AI response

        Returns:
            Structured analysis data
        """
        analysis = {
            "summary": "",
            "main_topics": [],
            "keywords": [],
            "category": "",
            "suggestions": [],
            "raw_response": response
        }

        try:
            # Extract main topics
            topics_match = re.search(r'(?:main topics?|themes?).*?:\s*(.+?)(?:\n\d+\.|$)', response, re.IGNORECASE | re.DOTALL)
            if topics_match:
                topics_text = topics_match.group(1).strip()
                analysis["main_topics"] = [topic.strip() for topic in re.split(r'[,;]|\n-', topics_text) if topic.strip()]

            # Extract keywords
            keywords_match = re.search(r'(?:keywords?|tags?).*?:\s*(.+?)(?:\n\d+\.|$)', response, re.IGNORECASE | re.DOTALL)
            if keywords_match:
                keywords_text = keywords_match.group(1).strip()
                analysis["keywords"] = [kw.strip() for kw in re.split(r'[,;]|\n-', keywords_text) if kw.strip()]

            # Extract category
            category_match = re.search(r'(?:category|genre|type).*?:\s*(.+?)(?:\n|$)', response, re.IGNORECASE)
            if category_match:
                analysis["category"] = category_match.group(1).strip()

            # Extract summary (first paragraph or sentence)
            lines = response.split('\n')
            for line in lines:
                if line.strip() and not line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
                    analysis["summary"] = line.strip()
                    break

        except Exception as e:
            self.logger.warning(f"Failed to parse analysis response: {e}")

        return analysis
        
    @handle_exceptions(context="title generation")
    def generate_title(
        self,
        content: str,
        content_type: str = "general",
        max_length: int = 100,
        style: str = "engaging"
    ) -> str:
        """Generate an engaging title from content using AI.

        Args:
            content: Content to generate title from
            content_type: Type of content (video, article, filename, etc.)
            max_length: Maximum title length
            style: Title style (engaging, professional, descriptive, etc.)

        Returns:
            Generated title

        Raises:
            DeepSeekError: If title generation fails
        """
        try:
            if not self.is_authenticated:
                raise DeepSeekError("Not authenticated with DeepSeek API")

            if not content or not content.strip():
                raise DeepSeekError("No content provided for title generation")

            self.logger.info(f"Generating {style} title for {content_type} content")

            # Build title generation prompt
            title_prompt = self._build_title_prompt(content, content_type, max_length, style)

            messages = [
                {"role": "system", "content": "You are an expert at creating engaging, descriptive titles. Generate titles that are clear, compelling, and appropriate for the content."},
                {"role": "user", "content": title_prompt}
            ]

            # Make API request
            response = self._make_api_request(messages, max_tokens=150, temperature=0.8)

            # Clean and validate title
            title = self._clean_title(response, max_length)

            if not title:
                raise DeepSeekError("Generated title is empty")

            self.logger.info(f"Generated title: {title}")
            return title

        except Exception as e:
            self.logger.error(f"Title generation failed: {e}")
            raise DeepSeekError(f"Title generation failed: {e}")

    def _build_title_prompt(self, content: str, content_type: str, max_length: int, style: str) -> str:
        """Build title generation prompt.

        Args:
            content: Content to generate title from
            content_type: Type of content
            max_length: Maximum title length
            style: Title style

        Returns:
            Formatted prompt string
        """
        style_instructions = {
            "engaging": "Make it catchy and engaging to attract viewers",
            "professional": "Keep it professional and formal",
            "descriptive": "Focus on accurately describing the content",
            "creative": "Be creative and unique while staying relevant",
            "seo": "Optimize for search engines with relevant keywords"
        }

        style_instruction = style_instructions.get(style, style_instructions["engaging"])

        if content_type == "video":
            type_instruction = "This is for a video title. Make it compelling for video viewers."
        elif content_type == "filename":
            type_instruction = "This is for renaming a file. Make it descriptive and filesystem-friendly."
        elif content_type == "article":
            type_instruction = "This is for an article title. Make it informative and engaging."
        else:
            type_instruction = "Generate an appropriate title for this content."

        prompt = f"""
{self.title_prompt}

Content to analyze:
"{content}"

Requirements:
- Maximum length: {max_length} characters
- Style: {style_instruction}
- Purpose: {type_instruction}
- Return only the title, no additional text or quotes

Generate a single, perfect title:
"""

        return prompt

    def _clean_title(self, raw_title: str, max_length: int) -> str:
        """Clean and validate generated title.

        Args:
            raw_title: Raw title from AI
            max_length: Maximum allowed length

        Returns:
            Cleaned title
        """
        if not raw_title:
            return ""

        # Remove quotes and extra whitespace
        title = raw_title.strip().strip('"\'')

        # Remove common prefixes
        prefixes_to_remove = [
            "Title:", "Generated title:", "Suggested title:",
            "Here's a title:", "A good title would be:",
            "The title is:", "Title suggestion:"
        ]

        for prefix in prefixes_to_remove:
            if title.lower().startswith(prefix.lower()):
                title = title[len(prefix):].strip()

        # Truncate if too long
        if len(title) > max_length:
            title = title[:max_length].rsplit(' ', 1)[0]
            if not title.endswith('.'):
                title += '...'

        # Remove invalid filename characters if this is for a filename
        title = re.sub(r'[<>:"/\\|?*]', '_', title)

        return title.strip()
        
    @handle_exceptions(context="description generation")
    def generate_description(
        self,
        title: str,
        content_hints: Optional[str] = None,
        max_length: int = 500,
        include_keywords: bool = True,
        style: str = "informative"
    ) -> str:
        """Generate a description for content using AI.

        Args:
            title: Content title
            content_hints: Additional content hints or context
            max_length: Maximum description length
            include_keywords: Whether to include relevant keywords
            style: Description style (informative, engaging, professional)

        Returns:
            Generated description

        Raises:
            DeepSeekError: If description generation fails
        """
        try:
            if not self.is_authenticated:
                raise DeepSeekError("Not authenticated with DeepSeek API")

            if not title or not title.strip():
                raise DeepSeekError("No title provided for description generation")

            self.logger.info(f"Generating description for title: {title}")

            # Build description generation prompt
            description_prompt = self._build_description_prompt(
                title, content_hints, max_length, include_keywords, style
            )

            messages = [
                {"role": "system", "content": "You are an expert content writer who creates engaging, informative descriptions. Write descriptions that are clear, compelling, and provide value to readers."},
                {"role": "user", "content": description_prompt}
            ]

            # Make API request
            response = self._make_api_request(messages, max_tokens=max_length + 100, temperature=0.7)

            # Clean and validate description
            description = self._clean_description(response, max_length)

            if not description:
                raise DeepSeekError("Generated description is empty")

            self.logger.info("Description generated successfully")
            return description

        except Exception as e:
            self.logger.error(f"Description generation failed: {e}")
            raise DeepSeekError(f"Description generation failed: {e}")

    def _build_description_prompt(
        self,
        title: str,
        content_hints: Optional[str],
        max_length: int,
        include_keywords: bool,
        style: str
    ) -> str:
        """Build description generation prompt.

        Args:
            title: Content title
            content_hints: Additional content hints
            max_length: Maximum description length
            include_keywords: Whether to include keywords
            style: Description style

        Returns:
            Formatted prompt string
        """
        style_instructions = {
            "informative": "Focus on providing clear, factual information",
            "engaging": "Make it engaging and compelling to read",
            "professional": "Keep it professional and formal",
            "casual": "Use a casual, friendly tone",
            "educational": "Focus on educational value and learning"
        }

        style_instruction = style_instructions.get(style, style_instructions["informative"])

        prompt = f"""
{self.description_prompt}

Title: "{title}"
"""

        if content_hints:
            prompt += f"\nAdditional context: {content_hints}"

        prompt += f"""

Requirements:
- Maximum length: {max_length} characters
- Style: {style_instruction}
- Make it suitable for video descriptions or content summaries
"""

        if include_keywords:
            prompt += "- Include relevant keywords naturally"

        prompt += "\n\nGenerate a compelling description:"

        return prompt

    def _clean_description(self, raw_description: str, max_length: int) -> str:
        """Clean and validate generated description.

        Args:
            raw_description: Raw description from AI
            max_length: Maximum allowed length

        Returns:
            Cleaned description
        """
        if not raw_description:
            return ""

        # Remove common prefixes and clean up
        description = raw_description.strip()

        prefixes_to_remove = [
            "Description:", "Generated description:", "Here's a description:",
            "The description is:", "Description suggestion:"
        ]

        for prefix in prefixes_to_remove:
            if description.lower().startswith(prefix.lower()):
                description = description[len(prefix):].strip()

        # Truncate if too long, but try to end at a sentence
        if len(description) > max_length:
            truncated = description[:max_length]
            # Try to end at the last complete sentence
            last_period = truncated.rfind('.')
            if last_period > max_length * 0.8:  # If we can keep most of the content
                description = truncated[:last_period + 1]
            else:
                # Otherwise, truncate at word boundary
                description = truncated.rsplit(' ', 1)[0] + '...'

        return description.strip()
        
    def suggest_filename(
        self,
        original_filename: str,
        content_analysis: Optional[Dict] = None
    ) -> List[str]:
        """Suggest improved filenames based on content analysis.

        Args:
            original_filename: Original filename
            content_analysis: Optional content analysis results

        Returns:
            List of suggested filenames
        """
        try:
            if not self.is_authenticated:
                raise DeepSeekError("Not authenticated with DeepSeek API")

            self.logger.info(f"Generating filename suggestions for: {original_filename}")

            # Build filename suggestion prompt
            prompt = f"""
Suggest 3-5 improved filenames for this file: "{original_filename}"

Requirements:
- Keep the original file extension
- Make filenames descriptive and meaningful
- Use filesystem-safe characters only
- Keep reasonable length (under 100 characters)
- Follow good naming conventions
"""

            if content_analysis:
                if content_analysis.get("main_topics"):
                    prompt += f"\nMain topics: {', '.join(content_analysis['main_topics'])}"
                if content_analysis.get("keywords"):
                    prompt += f"\nKeywords: {', '.join(content_analysis['keywords'])}"

            prompt += "\n\nProvide only the filenames, one per line:"

            messages = [
                {"role": "system", "content": "You are an expert at creating clear, descriptive filenames that follow best practices."},
                {"role": "user", "content": prompt}
            ]

            response = self._make_api_request(messages, max_tokens=300)

            # Parse suggestions
            suggestions = []
            for line in response.split('\n'):
                line = line.strip()
                if line and not line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '*')):
                    # Clean the suggestion
                    suggestion = re.sub(r'^[\d\.\-\*\s]+', '', line).strip()
                    if suggestion:
                        suggestions.append(suggestion)

            return suggestions[:5]  # Return max 5 suggestions

        except Exception as e:
            self.logger.error(f"Filename suggestion failed: {e}")
            return []

    def batch_analyze_files(
        self,
        file_paths: List[str],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Dict[str, Dict]:
        """Analyze multiple files in batch.

        Args:
            file_paths: List of file paths to analyze
            progress_callback: Progress callback function

        Returns:
            Dictionary mapping file paths to analysis results
        """
        results = {}
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            try:
                if progress_callback:
                    progress_callback(i + 1, total_files)

                # Extract filename for analysis
                filename = Path(file_path).name
                analysis = self.analyze_content(filename, content_type="filename")
                results[file_path] = analysis

                # Small delay to avoid rate limiting
                time.sleep(0.1)

            except Exception as e:
                self.logger.warning(f"Failed to analyze {file_path}: {e}")
                results[file_path] = {"error": str(e)}

        return results

    def check_authentication(self) -> bool:
        """Check if client is authenticated.

        Returns:
            True if authenticated, False otherwise
        """
        return self.is_authenticated

    def get_api_status(self) -> Dict[str, Any]:
        """Get API status and usage information.

        Returns:
            Dictionary with API status
        """
        return {
            "authenticated": self.is_authenticated,
            "api_available": OPENAI_AVAILABLE or REQUESTS_AVAILABLE,
            "model": self.model,
            "base_url": self.base_url,
            "usage_stats": self.usage_stats.copy(),
            "last_request": self.usage_stats["last_request"].isoformat() if self.usage_stats["last_request"] else None
        }

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get detailed usage statistics.

        Returns:
            Dictionary with usage statistics
        """
        return self.usage_stats.copy()

    def reset_usage_stats(self):
        """Reset usage statistics."""
        self.usage_stats = {
            "requests_made": 0,
            "tokens_used": 0,
            "errors": 0,
            "last_request": None
        }
        self.logger.info("Usage statistics reset")

    def test_api_connection(self) -> Dict[str, Any]:
        """Test API connection and return detailed results.

        Returns:
            Dictionary with test results
        """
        test_result = {
            "success": False,
            "response_time": None,
            "error": None,
            "api_available": OPENAI_AVAILABLE or REQUESTS_AVAILABLE
        }

        try:
            start_time = time.time()

            # Test with simple request
            test_response = self._make_api_request(
                messages=[{"role": "user", "content": "Respond with just the word 'OK'"}],
                max_tokens=5
            )

            end_time = time.time()
            test_result["response_time"] = end_time - start_time
            test_result["success"] = "ok" in test_response.lower()

            if not test_result["success"]:
                test_result["error"] = f"Unexpected response: {test_response}"

        except Exception as e:
            test_result["error"] = str(e)

        return test_result
