# Desktop Assistant

A powerful desktop application that automates email processing, file management, and YouTube uploads using AI integration.

## Features

- **Email Processing**: Automatically check emails for specific senders and subjects
- **Video Download**: Download video attachments from emails
- **AI Integration**: Use DeepSeek API for intelligent file renaming and content generation
- **YouTube Upload**: Automatically upload videos to YouTube with AI-generated titles and descriptions
- **File Management**: Organize and manage downloaded files
- **GUI Interface**: User-friendly interface with progress tracking

## Installation

### Prerequisites

- Python 3.8 or higher
- Windows 10/11 (primary target platform)

### Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd desktop-assistant
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # On Windows
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**
   ```bash
   python main.py
   ```

## Configuration

### API Keys Required

The application requires the following API keys:

1. **DeepSeek API Key**
   - Sign up at [DeepSeek](https://platform.deepseek.com/)
   - Generate an API key
   - Add it through the application settings

2. **YouTube Data API**
   - Create a project in [Google Cloud Console](https://console.cloud.google.com/)
   - Enable YouTube Data API v3
   - Create OAuth 2.0 credentials
   - Download the client configuration

3. **Email Configuration**
   - Configure your email server settings
   - For Gmail, you may need to enable "Less secure app access" or use App Passwords

### Settings

The application stores settings in `~/.desktop_assistant/settings.json`. You can modify:

- Email server configuration
- Download folders
- API endpoints
- File size limits
- Default YouTube settings

## Usage

### Basic Workflow

1. **Configure Settings**: Set up your email and API credentials
2. **Check Email**: Click "Check Email & Download Videos" to scan for new videos
3. **Process Files**: Use DeepSeek integration to rename files intelligently
4. **Upload to YouTube**: Automatically upload with AI-generated metadata

### Manual Operations

Each component can be used independently:

- **Email Handler**: Check and download email attachments
- **File Manager**: Organize and rename files
- **DeepSeek Client**: Generate titles and descriptions
- **YouTube Uploader**: Upload videos with custom metadata

## Development

### Project Structure

```
Desktop Assistant/
├── main.py                 # Main application entry point
├── gui/                    # GUI components
│   ├── main_window.py
│   ├── task_buttons.py
│   └── progress_display.py
├── modules/                # Core functionality modules
│   ├── email_handler.py
│   ├── file_manager.py
│   ├── deepseek_client.py
│   └── youtube_uploader.py
├── config/                 # Configuration files
│   ├── settings.py
│   └── api_keys.py
├── utils/                  # Utility functions
│   ├── logger.py
│   └── error_handler.py
└── tests/                  # Test files
```

### Development Stages

The project is developed in 10 stages:

1. **Project Planning & Setup** ✅
2. **Core Infrastructure Development** (In Progress)
3. **Email Integration Module**
4. **File Management Module**
5. **DeepSeek Integration**
6. **YouTube Integration Module**
7. **GUI Development**
8. **Integration & Testing**
9. **Optimization & Polish**
10. **Packaging & Distribution**

### Running Tests

```bash
pytest tests/
```

### Building Executable

```bash
pyinstaller --onefile --windowed main.py
```

## Security

- API keys are stored encrypted in local configuration
- OAuth tokens are managed securely
- File operations are sandboxed to designated folders
- All external API calls are validated

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **API Authentication**: Check API keys and network connectivity
3. **File Permissions**: Ensure write access to download folders
4. **Email Connection**: Verify email server settings and credentials

### Logs

Application logs are stored in `~/.desktop_assistant/desktop_assistant.log`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review application logs
- Create an issue in the repository

## Version History

- **v1.0.0**: Initial release with core functionality
  - Email processing
  - File management
  - DeepSeek integration
  - YouTube uploading
  - GUI interface
