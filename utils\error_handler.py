"""
Error Handling Utilities for Desktop Assistant

This module provides error handling utilities and custom exceptions.
"""

import logging
import traceback
import functools
from typing import Callable, Any, Optional
from tkinter import messagebox

class DesktopAssistantError(Exception):
    """Base exception for Desktop Assistant."""
    pass

class EmailError(DesktopAssistantError):
    """Exception for email-related errors."""
    pass

class FileError(DesktopAssistantError):
    """Exception for file-related errors."""
    pass

class APIError(DesktopAssistantError):
    """Exception for API-related errors."""
    pass

class YouTubeError(DesktopAssistantError):
    """Exception for YouTube-related errors."""
    pass

class DeepSeekError(DesktopAssistantError):
    """Exception for DeepSeek-related errors."""
    pass

class ErrorHandler:
    """Centralized error handling class."""
    
    def __init__(self, show_gui_errors: bool = True):
        """Initialize error handler.
        
        Args:
            show_gui_errors: Whether to show error dialogs in GUI
        """
        self.logger = logging.getLogger(__name__)
        self.show_gui_errors = show_gui_errors
        
    def handle_error(
        self, 
        error: Exception, 
        context: str = "", 
        show_dialog: bool = None,
        critical: bool = False
    ):
        """Handle an error with logging and optional GUI notification.
        
        Args:
            error: Exception that occurred
            context: Context description where error occurred
            show_dialog: Whether to show error dialog (overrides default)
            critical: Whether this is a critical error
        """
        # Determine error level
        if critical:
            log_level = logging.CRITICAL
            dialog_title = "Critical Error"
        elif isinstance(error, (EmailError, FileError, APIError, YouTubeError, DeepSeekError)):
            log_level = logging.ERROR
            dialog_title = "Application Error"
        else:
            log_level = logging.WARNING
            dialog_title = "Warning"
            
        # Create error message
        if context:
            message = f"{context}: {str(error)}"
        else:
            message = str(error)
            
        # Log the error
        self.logger.log(log_level, message, exc_info=True)
        
        # Show GUI dialog if requested
        show_dialog = show_dialog if show_dialog is not None else self.show_gui_errors
        if show_dialog:
            try:
                messagebox.showerror(dialog_title, message)
            except Exception:
                # If GUI is not available, just log
                self.logger.error("Failed to show error dialog")
                
    def handle_unexpected_error(self, error: Exception, context: str = ""):
        """Handle unexpected errors with full traceback.
        
        Args:
            error: Exception that occurred
            context: Context description
        """
        tb_str = traceback.format_exc()
        
        if context:
            message = f"Unexpected error in {context}: {str(error)}"
        else:
            message = f"Unexpected error: {str(error)}"
            
        self.logger.critical(f"{message}\n{tb_str}")
        
        if self.show_gui_errors:
            try:
                detailed_message = f"{message}\n\nPlease check the log file for more details."
                messagebox.showerror("Unexpected Error", detailed_message)
            except Exception:
                pass

# Global error handler instance
error_handler = ErrorHandler()

def handle_exceptions(
    context: str = "",
    show_dialog: bool = None,
    reraise: bool = True,
    default_return: Any = None
):
    """Decorator for handling exceptions in functions.
    
    Args:
        context: Context description for the error
        show_dialog: Whether to show error dialog
        reraise: Whether to reraise the exception after handling
        default_return: Default value to return if exception occurs and not reraising
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_context = context or f"function {func.__name__}"
                error_handler.handle_error(e, func_context, show_dialog)
                
                if reraise:
                    raise
                else:
                    return default_return
                    
        return wrapper
    return decorator

def safe_execute(
    func: Callable,
    *args,
    context: str = "",
    show_dialog: bool = None,
    default_return: Any = None,
    **kwargs
) -> Any:
    """Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        context: Context description
        show_dialog: Whether to show error dialog
        default_return: Default return value on error
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        func_context = context or f"function {func.__name__}"
        error_handler.handle_error(e, func_context, show_dialog)
        return default_return

def validate_file_path(file_path: str, must_exist: bool = True) -> bool:
    """Validate a file path.
    
    Args:
        file_path: Path to validate
        must_exist: Whether the file must exist
        
    Returns:
        True if valid, False otherwise
        
    Raises:
        FileError: If validation fails
    """
    from pathlib import Path
    
    try:
        path = Path(file_path)
        
        if must_exist and not path.exists():
            raise FileError(f"File does not exist: {file_path}")
            
        if must_exist and not path.is_file():
            raise FileError(f"Path is not a file: {file_path}")
            
        # Check if parent directory exists for new files
        if not must_exist and not path.parent.exists():
            raise FileError(f"Parent directory does not exist: {path.parent}")
            
        return True
        
    except Exception as e:
        if isinstance(e, FileError):
            raise
        else:
            raise FileError(f"Invalid file path: {file_path}") from e

def validate_api_key(api_key: str, service: str) -> bool:
    """Validate an API key format.
    
    Args:
        api_key: API key to validate
        service: Service name (deepseek, youtube, etc.)
        
    Returns:
        True if valid, False otherwise
        
    Raises:
        APIError: If validation fails
    """
    if not api_key or not isinstance(api_key, str):
        raise APIError(f"Invalid API key for {service}: empty or not a string")
        
    api_key = api_key.strip()
    
    if service.lower() == "deepseek":
        if not api_key.startswith("sk-") or len(api_key) < 20:
            raise APIError("DeepSeek API key must start with 'sk-' and be at least 20 characters")
    elif service.lower() == "youtube":
        if len(api_key) < 10:
            raise APIError("YouTube API key must be at least 10 characters")
    else:
        if len(api_key) < 5:
            raise APIError(f"API key for {service} must be at least 5 characters")
            
    return True
