"""
Logging Configuration for Desktop Assistant

This module sets up logging for the application.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

def setup_logger(
    name: str = "desktop_assistant",
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """Set up application logging.
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Log file path (optional)
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup files to keep
        
    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if log_file specified)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Set up default log file in user's config directory
    else:
        config_dir = Path.home() / ".desktop_assistant"
        config_dir.mkdir(exist_ok=True)
        
        log_file = config_dir / "desktop_assistant.log"
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Log startup message
    logger.info(f"Desktop Assistant logging initialized - Level: {level}")
    logger.info(f"Log file: {log_file}")
    
    return logger

class GUILogHandler(logging.Handler):
    """Custom log handler that sends logs to GUI progress display."""
    
    def __init__(self, progress_display):
        """Initialize GUI log handler.
        
        Args:
            progress_display: ProgressDisplay widget instance
        """
        super().__init__()
        self.progress_display = progress_display
        
    def emit(self, record):
        """Emit a log record to the GUI.
        
        Args:
            record: LogRecord instance
        """
        try:
            message = self.format(record)
            level = record.levelname
            
            # Send to appropriate GUI method based on level
            if level == "ERROR" or level == "CRITICAL":
                self.progress_display.log_error(message)
            elif level == "WARNING":
                self.progress_display.log_warning(message)
            elif level == "INFO":
                self.progress_display.log_info(message)
            else:  # DEBUG and others
                self.progress_display.log_info(message)
                
        except Exception:
            # Avoid infinite recursion if logging fails
            pass

def add_gui_handler(logger: logging.Logger, progress_display):
    """Add GUI handler to existing logger.
    
    Args:
        logger: Logger instance
        progress_display: ProgressDisplay widget instance
    """
    gui_handler = GUILogHandler(progress_display)
    gui_handler.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    gui_handler.setFormatter(formatter)
    
    logger.addHandler(gui_handler)

def get_logger(name: str = "desktop_assistant") -> logging.Logger:
    """Get the application logger.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)

# Performance logging decorator
def log_performance(func):
    """Decorator to log function execution time.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        logger = get_logger()
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.debug(f"{func.__name__} completed in {duration:.2f} seconds")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"{func.__name__} failed after {duration:.2f} seconds: {e}")
            raise
            
    return wrapper
