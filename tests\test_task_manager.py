"""
Tests for Task Manager

This module contains tests for the task management system.
"""

import unittest
import time
from unittest.mock import Mock

from utils.task_manager import TaskManager, Task, TaskStatus

class TestTaskManager(unittest.TestCase):
    """Test cases for TaskManager class."""
    
    def setUp(self):
        """Set up test environment."""
        self.progress_callback = Mock()
        self.task_manager = TaskManager(progress_callback=self.progress_callback)
        
    def test_add_task(self):
        """Test adding tasks to the manager."""
        # Add a simple task
        def test_function():
            return "test_result"
            
        task = self.task_manager.add_task(
            task_id="test_task",
            name="Test Task",
            function=test_function
        )
        
        # Verify task was added
        self.assertEqual(task.id, "test_task")
        self.assertEqual(task.name, "Test Task")
        self.assertEqual(task.status, TaskStatus.PENDING)
        
        # Verify task is in manager
        retrieved_task = self.task_manager.get_task("test_task")
        self.assertEqual(retrieved_task, task)
        
    def test_remove_task(self):
        """Test removing tasks from the manager."""
        # Add a task
        def test_function():
            pass
            
        self.task_manager.add_task(
            task_id="test_task",
            name="Test Task",
            function=test_function
        )
        
        # Remove the task
        result = self.task_manager.remove_task("test_task")
        self.assertTrue(result)
        
        # Verify task was removed
        self.assertIsNone(self.task_manager.get_task("test_task"))
        
        # Try to remove non-existing task
        result = self.task_manager.remove_task("non_existing")
        self.assertFalse(result)
        
    def test_execute_task(self):
        """Test executing a single task."""
        # Create a test function
        def test_function(value):
            return value * 2
            
        # Add task
        task = self.task_manager.add_task(
            task_id="test_task",
            name="Test Task",
            function=test_function,
            args=(5,)
        )
        
        # Execute task
        result = self.task_manager.execute_task(task)
        
        # Verify execution
        self.assertTrue(result)
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        self.assertEqual(task.result, 10)
        self.assertIsNotNone(task.start_time)
        self.assertIsNotNone(task.end_time)
        
    def test_execute_task_with_error(self):
        """Test executing a task that raises an error."""
        # Create a function that raises an error
        def error_function():
            raise ValueError("Test error")
            
        # Add task
        task = self.task_manager.add_task(
            task_id="error_task",
            name="Error Task",
            function=error_function
        )
        
        # Execute task
        result = self.task_manager.execute_task(task)
        
        # Verify error handling
        self.assertFalse(result)
        self.assertEqual(task.status, TaskStatus.FAILED)
        self.assertIsInstance(task.error, ValueError)
        
    def test_task_dependencies(self):
        """Test task dependency resolution."""
        # Create test functions
        def task1():
            return "task1_result"
            
        def task2():
            return "task2_result"
            
        def task3():
            return "task3_result"
            
        # Add tasks with dependencies
        self.task_manager.add_task("task1", "Task 1", task1)
        self.task_manager.add_task("task2", "Task 2", task2, dependencies=["task1"])
        self.task_manager.add_task("task3", "Task 3", task3, dependencies=["task1", "task2"])
        
        # Initially, only task1 should be ready
        ready_tasks = self.task_manager.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, "task1")
        
        # Execute task1
        task1_obj = self.task_manager.get_task("task1")
        self.task_manager.execute_task(task1_obj)
        
        # Now task2 should be ready
        ready_tasks = self.task_manager.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, "task2")
        
        # Execute task2
        task2_obj = self.task_manager.get_task("task2")
        self.task_manager.execute_task(task2_obj)
        
        # Now task3 should be ready
        ready_tasks = self.task_manager.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, "task3")
        
    def test_cancel_task(self):
        """Test cancelling a pending task."""
        def test_function():
            pass
            
        # Add task
        self.task_manager.add_task("test_task", "Test Task", test_function)
        
        # Cancel task
        result = self.task_manager.cancel_task("test_task")
        self.assertTrue(result)
        
        # Verify task was cancelled
        task = self.task_manager.get_task("test_task")
        self.assertEqual(task.status, TaskStatus.CANCELLED)
        
    def test_status_summary(self):
        """Test getting status summary."""
        def test_function():
            return "result"
            
        def error_function():
            raise ValueError("Error")
            
        # Add various tasks
        self.task_manager.add_task("task1", "Task 1", test_function)
        self.task_manager.add_task("task2", "Task 2", test_function)
        self.task_manager.add_task("task3", "Task 3", error_function)
        
        # Execute tasks
        self.task_manager.execute_task(self.task_manager.get_task("task1"))
        self.task_manager.execute_task(self.task_manager.get_task("task3"))
        self.task_manager.cancel_task("task2")
        
        # Get status summary
        summary = self.task_manager.get_status_summary()
        
        # Verify summary
        self.assertEqual(summary[TaskStatus.COMPLETED.value], 1)
        self.assertEqual(summary[TaskStatus.FAILED.value], 1)
        self.assertEqual(summary[TaskStatus.CANCELLED.value], 1)
        self.assertEqual(summary[TaskStatus.PENDING.value], 0)
        
    def test_clear_completed(self):
        """Test clearing completed tasks."""
        def test_function():
            return "result"
            
        # Add and execute tasks
        self.task_manager.add_task("task1", "Task 1", test_function)
        self.task_manager.add_task("task2", "Task 2", test_function)
        
        self.task_manager.execute_task(self.task_manager.get_task("task1"))
        
        # Clear completed tasks
        self.task_manager.clear_completed()
        
        # Verify only pending task remains
        self.assertIsNone(self.task_manager.get_task("task1"))
        self.assertIsNotNone(self.task_manager.get_task("task2"))

if __name__ == '__main__':
    unittest.main()
