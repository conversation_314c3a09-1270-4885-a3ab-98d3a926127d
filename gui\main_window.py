"""
Main Window for Desktop Assistant

This module contains the main application window and GUI framework.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Optional

from .task_buttons import TaskButtonPanel
from .progress_display import ProgressDisplay
from .settings_dialog import SettingsDialog
from utils.task_manager import TaskManager
from utils.icon_generator import setup_app_icon
from utils.logger import add_gui_handler
from .styles import setup_app_styling

class MainWindow:
    """Main application window class."""
    
    def __init__(self, settings):
        """Initialize the main window.
        
        Args:
            settings: Application settings object
        """
        self.settings = settings
        self.logger = logging.getLogger(__name__)

        # Create main window
        self.root = tk.Tk()
        self.root.title("Desktop Assistant v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Set window icon
        self._setup_icon()

        # Initialize task manager
        self.task_manager = TaskManager(progress_callback=self._on_task_progress)

        # Set up styling
        self.styles = setup_app_styling(self.root)

        self._setup_ui()
        self._setup_menu()

        # Connect GUI logging
        add_gui_handler(self.logger, self.progress_display)

    def _setup_icon(self):
        """Set up application icon."""
        try:
            icon_path = setup_app_icon()
            if icon_path and icon_path.endswith('.ico'):
                self.root.iconbitmap(icon_path)
                self.logger.info(f"Application icon set: {icon_path}")
        except Exception as e:
            self.logger.warning(f"Could not set application icon: {e}")

    def _on_task_progress(self, task_name: str, progress: float):
        """Handle task progress updates.

        Args:
            task_name: Name of the task
            progress: Progress percentage (0-100)
        """
        self.progress_display.update_progress(progress, f"Running: {task_name}")
        self.update_status(f"Running: {task_name} ({progress:.1f}%)")

    def _setup_ui(self):
        """Set up the user interface components."""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title label
        title_label = ttk.Label(
            main_frame, 
            text="Desktop Assistant", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Task buttons panel
        self.task_panel = TaskButtonPanel(main_frame, self)
        self.task_panel.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Progress display
        self.progress_display = ProgressDisplay(main_frame)
        self.progress_display.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def _setup_menu(self):
        """Set up the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Settings", command=self._open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)
        
    def _open_settings(self):
        """Open settings dialog."""
        try:
            settings_dialog = SettingsDialog(self.root, self.settings)
            self.logger.info("Settings dialog opened")
        except Exception as e:
            self.logger.error(f"Failed to open settings dialog: {e}")
            messagebox.showerror("Error", f"Failed to open settings: {e}")
        
    def _show_about(self):
        """Show about dialog."""
        about_text = """Desktop Assistant v1.0
        
A desktop application for automating email processing,
file management, and YouTube uploads.

Features:
• Email checking and video downloading
• File renaming with DeepSeek integration
• YouTube video uploading
• Task automation"""
        
        messagebox.showinfo("About Desktop Assistant", about_text)
        
    def _on_closing(self):
        """Handle window closing event."""
        if messagebox.askokcancel("Quit", "Do you want to quit Desktop Assistant?"):
            self.logger.info("Application closing...")
            self.root.destroy()
            
    def update_status(self, message: str):
        """Update the status bar message.
        
        Args:
            message: Status message to display
        """
        self.status_var.set(message)
        self.root.update_idletasks()
        
    def run(self):
        """Start the main application loop."""
        self.logger.info("Starting GUI main loop...")
        
        # Set up window closing protocol
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # Start the main loop
        self.root.mainloop()
