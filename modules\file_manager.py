"""
File Manager Module for Desktop Assistant

This module handles file operations including:
- File downloading from URLs
- Intelligent file renaming
- File organization and management
- File validation and metadata extraction
- File operations (move, copy, delete)

Implemented in Stage 4.
"""

import logging
import shutil
import os
import re
import hashlib
import mimetypes
from typing import List, Optional, Dict, Callable, Tuple
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse, unquote
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

from utils.error_handler import FileError, handle_exceptions, validate_file_path
from config.settings import Settings

class FileManager:
    """Handles file operations for the Desktop Assistant."""

    def __init__(self, settings: Settings):
        """Initialize file manager.

        Args:
            settings: Application settings instance
        """
        self.settings = settings
        self.logger = logging.getLogger(__name__)

        # Initialize file type detection
        mimetypes.init()

        # Common video file extensions
        self.video_extensions = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm',
            '.m4v', '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.mts'
        }

        # File size limits (in bytes)
        self.max_file_size = self.settings.get('files.max_file_size_mb', 500) * 1024 * 1024
        
    @handle_exceptions(context="file download")
    def download_file(
        self,
        url: str,
        destination: Optional[str] = None,
        filename: Optional[str] = None,
        progress_callback: Optional[Callable[[int, int], None]] = None,
        overwrite: bool = False
    ) -> str:
        """Download a file from URL.

        Args:
            url: URL to download from
            destination: Destination folder
            filename: Custom filename (optional)
            progress_callback: Callback for progress updates (downloaded, total)
            overwrite: Whether to overwrite existing files

        Returns:
            Path to downloaded file

        Raises:
            FileError: If download fails
        """
        try:
            self.logger.info(f"Starting download from: {url}")

            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise FileError(f"Invalid URL: {url}")

            # Set destination folder
            if destination is None:
                destination = self.settings.get('files.temp_folder')

            dest_path = Path(destination)
            dest_path.mkdir(parents=True, exist_ok=True)

            # Determine filename
            if filename is None:
                # Extract filename from URL
                filename = unquote(Path(parsed_url.path).name)
                if not filename or '.' not in filename:
                    # Generate filename based on URL and timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"download_{timestamp}.tmp"

            # Ensure unique filename if not overwriting
            file_path = dest_path / filename
            if not overwrite:
                file_path = self._get_unique_filename(file_path)

            # Download file
            if REQUESTS_AVAILABLE:
                downloaded_path = self._download_with_requests(url, file_path, progress_callback)
            else:
                downloaded_path = self._download_with_urllib(url, file_path, progress_callback)

            # Validate downloaded file
            if not downloaded_path.exists() or downloaded_path.stat().st_size == 0:
                raise FileError("Downloaded file is empty or missing")

            # Check file size limit
            file_size = downloaded_path.stat().st_size
            if file_size > self.max_file_size:
                downloaded_path.unlink()  # Delete oversized file
                raise FileError(f"File size ({file_size / 1024 / 1024:.1f} MB) exceeds limit ({self.max_file_size / 1024 / 1024:.1f} MB)")

            self.logger.info(f"Successfully downloaded: {downloaded_path}")
            return str(downloaded_path)

        except Exception as e:
            self.logger.error(f"File download failed: {e}")
            raise FileError(f"Download failed: {e}")

    def _download_with_requests(
        self,
        url: str,
        file_path: Path,
        progress_callback: Optional[Callable[[int, int], None]]
    ) -> Path:
        """Download file using requests library.

        Args:
            url: URL to download
            file_path: Destination file path
            progress_callback: Progress callback

        Returns:
            Path to downloaded file
        """
        try:
            headers = {
                'User-Agent': 'Desktop Assistant File Downloader 1.0'
            }

            response = requests.get(url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        if progress_callback and total_size > 0:
                            progress_callback(downloaded, total_size)

            return file_path

        except requests.RequestException as e:
            raise FileError(f"HTTP download failed: {e}")

    def _download_with_urllib(
        self,
        url: str,
        file_path: Path,
        progress_callback: Optional[Callable[[int, int], None]]
    ) -> Path:
        """Download file using urllib (fallback).

        Args:
            url: URL to download
            file_path: Destination file path
            progress_callback: Progress callback

        Returns:
            Path to downloaded file
        """
        try:
            request = Request(url, headers={
                'User-Agent': 'Desktop Assistant File Downloader 1.0'
            })

            with urlopen(request, timeout=30) as response:
                total_size = int(response.headers.get('Content-Length', 0))
                downloaded = 0

                with open(file_path, 'wb') as f:
                    while True:
                        chunk = response.read(8192)
                        if not chunk:
                            break

                        f.write(chunk)
                        downloaded += len(chunk)

                        if progress_callback and total_size > 0:
                            progress_callback(downloaded, total_size)

            return file_path

        except (URLError, HTTPError) as e:
            raise FileError(f"URL download failed: {e}")

    def _get_unique_filename(self, file_path: Path) -> Path:
        """Generate a unique filename if file already exists.

        Args:
            file_path: Original file path

        Returns:
            Unique file path
        """
        if not file_path.exists():
            return file_path

        stem = file_path.stem
        suffix = file_path.suffix
        parent = file_path.parent
        counter = 1

        while True:
            new_path = parent / f"{stem}_{counter}{suffix}"
            if not new_path.exists():
                return new_path
            counter += 1
        
    @handle_exceptions(context="file rename")
    def rename_file(
        self,
        file_path: str,
        new_name: str,
        preserve_extension: bool = True,
        backup: bool = None,
        sanitize: bool = True
    ) -> str:
        """Rename a file with intelligent handling.

        Args:
            file_path: Current file path
            new_name: New filename
            preserve_extension: Whether to keep original extension
            backup: Whether to create backup (uses setting if None)
            sanitize: Whether to sanitize the filename

        Returns:
            New file path

        Raises:
            FileError: If rename fails
        """
        try:
            source_path = Path(file_path)
            validate_file_path(str(source_path), must_exist=True)

            # Sanitize new name if requested
            if sanitize:
                new_name = self._sanitize_filename(new_name)

            # Preserve extension if requested
            if preserve_extension and source_path.suffix:
                if not new_name.endswith(source_path.suffix):
                    new_name += source_path.suffix

            # Create new path
            new_path = source_path.parent / new_name

            # Check if target already exists
            if new_path.exists() and new_path != source_path:
                new_path = self._get_unique_filename(new_path)

            # Create backup if requested
            if backup is None:
                backup = self.settings.get('files.backup_before_rename', True)

            if backup:
                self._create_backup(source_path)

            # Perform rename
            source_path.rename(new_path)

            self.logger.info(f"Renamed file: {source_path} -> {new_path}")
            return str(new_path)

        except Exception as e:
            self.logger.error(f"File rename failed: {e}")
            raise FileError(f"Rename failed: {e}")

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        # Remove or replace invalid characters
        invalid_chars = r'[<>:"/\\|?*]'
        filename = re.sub(invalid_chars, '_', filename)

        # Remove control characters
        filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)

        # Trim whitespace and dots
        filename = filename.strip(' .')

        # Ensure filename is not empty
        if not filename:
            filename = "unnamed_file"

        # Limit length (Windows has 255 char limit)
        if len(filename) > 200:  # Leave room for extension
            filename = filename[:200]

        return filename

    def _create_backup(self, file_path: Path):
        """Create a backup of the file.

        Args:
            file_path: Path to file to backup
        """
        try:
            backup_folder = Path(self.settings.get('files.backup_folder'))
            backup_folder.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_folder / backup_name

            shutil.copy2(file_path, backup_path)
            self.logger.info(f"Created backup: {backup_path}")

        except Exception as e:
            self.logger.warning(f"Failed to create backup: {e}")

    def smart_rename(
        self,
        file_path: str,
        title: str = "",
        metadata: Optional[Dict] = None
    ) -> str:
        """Intelligently rename file based on title and metadata.

        Args:
            file_path: Current file path
            title: Suggested title for the file
            metadata: Additional metadata for naming

        Returns:
            New file path
        """
        try:
            source_path = Path(file_path)

            # Get naming pattern from settings
            pattern = self.settings.get('files.naming_pattern', '{title}_{date}')
            date_format = self.settings.get('files.date_format', '%Y%m%d_%H%M%S')

            # Prepare variables for pattern
            variables = {
                'title': title or source_path.stem,
                'date': datetime.now().strftime(date_format),
                'original_name': source_path.stem,
                'size': self._format_file_size(source_path.stat().st_size),
                'extension': source_path.suffix[1:] if source_path.suffix else ''
            }

            # Add metadata variables
            if metadata:
                variables.update(metadata)

            # Generate new name using pattern
            try:
                new_name = pattern.format(**variables)
            except KeyError as e:
                self.logger.warning(f"Invalid naming pattern variable: {e}")
                new_name = f"{variables['title']}_{variables['date']}"

            return self.rename_file(file_path, new_name)

        except Exception as e:
            self.logger.error(f"Smart rename failed: {e}")
            raise FileError(f"Smart rename failed: {e}")

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format.

        Args:
            size_bytes: Size in bytes

        Returns:
            Formatted size string
        """
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f}{unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f}TB"
        
    @handle_exceptions(context="file move")
    def move_file(
        self,
        source: str,
        destination: str,
        create_dirs: bool = True,
        overwrite: bool = False
    ) -> str:
        """Move a file to a new location.

        Args:
            source: Source file path
            destination: Destination path (file or directory)
            create_dirs: Whether to create destination directories
            overwrite: Whether to overwrite existing files

        Returns:
            New file path

        Raises:
            FileError: If move fails
        """
        try:
            source_path = Path(source)
            dest_path = Path(destination)

            validate_file_path(str(source_path), must_exist=True)

            # If destination is a directory, use original filename
            if dest_path.is_dir() or (not dest_path.suffix and not dest_path.exists()):
                dest_path = dest_path / source_path.name

            # Create destination directory if needed
            if create_dirs:
                dest_path.parent.mkdir(parents=True, exist_ok=True)

            # Check if destination exists
            if dest_path.exists() and not overwrite:
                dest_path = self._get_unique_filename(dest_path)

            # Perform move
            shutil.move(str(source_path), str(dest_path))

            self.logger.info(f"Moved file: {source_path} -> {dest_path}")
            return str(dest_path)

        except Exception as e:
            self.logger.error(f"File move failed: {e}")
            raise FileError(f"Move failed: {e}")

    @handle_exceptions(context="file copy")
    def copy_file(
        self,
        source: str,
        destination: str,
        create_dirs: bool = True,
        overwrite: bool = False
    ) -> str:
        """Copy a file to a new location.

        Args:
            source: Source file path
            destination: Destination path (file or directory)
            create_dirs: Whether to create destination directories
            overwrite: Whether to overwrite existing files

        Returns:
            New file path

        Raises:
            FileError: If copy fails
        """
        try:
            source_path = Path(source)
            dest_path = Path(destination)

            validate_file_path(str(source_path), must_exist=True)

            # If destination is a directory, use original filename
            if dest_path.is_dir() or (not dest_path.suffix and not dest_path.exists()):
                dest_path = dest_path / source_path.name

            # Create destination directory if needed
            if create_dirs:
                dest_path.parent.mkdir(parents=True, exist_ok=True)

            # Check if destination exists
            if dest_path.exists() and not overwrite:
                dest_path = self._get_unique_filename(dest_path)

            # Perform copy
            shutil.copy2(str(source_path), str(dest_path))

            self.logger.info(f"Copied file: {source_path} -> {dest_path}")
            return str(dest_path)

        except Exception as e:
            self.logger.error(f"File copy failed: {e}")
            raise FileError(f"Copy failed: {e}")

    @handle_exceptions(context="file delete")
    def delete_file(self, file_path: str, backup: bool = True) -> bool:
        """Delete a file with optional backup.

        Args:
            file_path: Path to file to delete
            backup: Whether to create backup before deletion

        Returns:
            True if deletion successful

        Raises:
            FileError: If deletion fails
        """
        try:
            path = Path(file_path)
            validate_file_path(str(path), must_exist=True)

            # Create backup if requested
            if backup:
                self._create_backup(path)

            # Delete file
            path.unlink()

            self.logger.info(f"Deleted file: {path}")
            return True

        except Exception as e:
            self.logger.error(f"File deletion failed: {e}")
            raise FileError(f"Deletion failed: {e}")
        
    @handle_exceptions(context="file validation")
    def validate_file(self, file_path: str) -> Dict[str, any]:
        """Validate a file and get its comprehensive properties.

        Args:
            file_path: Path to file to validate

        Returns:
            Dictionary with file properties and validation results

        Raises:
            FileError: If validation fails
        """
        try:
            path = Path(file_path)
            validate_file_path(str(path), must_exist=True)

            stat = path.stat()

            # Basic file information
            file_info = {
                "path": str(path),
                "name": path.name,
                "stem": path.stem,
                "extension": path.suffix.lower(),
                "size_bytes": stat.st_size,
                "size_formatted": self._format_file_size(stat.st_size),
                "created": datetime.fromtimestamp(stat.st_ctime),
                "modified": datetime.fromtimestamp(stat.st_mtime),
                "accessed": datetime.fromtimestamp(stat.st_atime),
                "is_file": path.is_file(),
                "is_directory": path.is_dir(),
                "is_symlink": path.is_symlink()
            }

            # File type detection
            file_info.update(self._detect_file_type(path))

            # Validation checks
            file_info["validation"] = self._perform_validation_checks(path, file_info)

            # Video-specific information
            if file_info.get("is_video", False):
                file_info.update(self._get_video_metadata(path))

            # File hash for integrity checking
            file_info["md5_hash"] = self._calculate_file_hash(path)

            return file_info

        except Exception as e:
            self.logger.error(f"File validation failed: {e}")
            raise FileError(f"Validation failed: {e}")

    def _detect_file_type(self, path: Path) -> Dict[str, any]:
        """Detect file type and MIME information.

        Args:
            path: File path

        Returns:
            Dictionary with type information
        """
        type_info = {}

        # MIME type detection
        mime_type, encoding = mimetypes.guess_type(str(path))
        type_info["mime_type"] = mime_type
        type_info["encoding"] = encoding

        # File type categories
        extension = path.suffix.lower()
        type_info["is_video"] = extension in self.video_extensions
        type_info["is_audio"] = extension in {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma'}
        type_info["is_image"] = extension in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        type_info["is_document"] = extension in {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'}
        type_info["is_archive"] = extension in {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}

        # Magic number detection (if available)
        if MAGIC_AVAILABLE:
            try:
                type_info["magic_type"] = magic.from_file(str(path))
                type_info["magic_mime"] = magic.from_file(str(path), mime=True)
            except Exception as e:
                self.logger.debug(f"Magic detection failed: {e}")

        return type_info

    def _perform_validation_checks(self, path: Path, file_info: Dict) -> Dict[str, any]:
        """Perform various validation checks on the file.

        Args:
            path: File path
            file_info: Basic file information

        Returns:
            Dictionary with validation results
        """
        validation = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }

        # Size validation
        max_size = self.settings.get('files.max_file_size_mb', 500) * 1024 * 1024
        if file_info["size_bytes"] > max_size:
            validation["warnings"].append(f"File size exceeds limit ({self._format_file_size(max_size)})")

        # Empty file check
        if file_info["size_bytes"] == 0:
            validation["errors"].append("File is empty")
            validation["is_valid"] = False

        # Extension validation for videos
        allowed_extensions = self.settings.get('files.allowed_extensions', [])
        if allowed_extensions and file_info["extension"] not in [ext.lower() for ext in allowed_extensions]:
            validation["warnings"].append(f"File extension not in allowed list: {allowed_extensions}")

        # Filename validation
        if not self._is_valid_filename(path.name):
            validation["warnings"].append("Filename contains potentially problematic characters")

        return validation

    def _is_valid_filename(self, filename: str) -> bool:
        """Check if filename is valid for the filesystem.

        Args:
            filename: Filename to check

        Returns:
            True if valid
        """
        # Check for invalid characters
        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, filename):
            return False

        # Check for reserved names (Windows)
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5',
            'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4',
            'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        if Path(filename).stem.upper() in reserved_names:
            return False

        return True

    def _get_video_metadata(self, path: Path) -> Dict[str, any]:
        """Extract video metadata (basic implementation).

        Args:
            path: Video file path

        Returns:
            Dictionary with video metadata
        """
        # Basic video information (can be enhanced with ffmpeg/mediainfo)
        video_info = {
            "duration": None,
            "resolution": None,
            "codec": None,
            "bitrate": None
        }

        # This is a placeholder - in a full implementation, you would use
        # libraries like ffmpeg-python or pymediainfo for detailed metadata
        self.logger.debug(f"Video metadata extraction not fully implemented for {path}")

        return video_info

    def _calculate_file_hash(self, path: Path, algorithm: str = "md5") -> str:
        """Calculate file hash for integrity checking.

        Args:
            path: File path
            algorithm: Hash algorithm (md5, sha1, sha256)

        Returns:
            Hex digest of file hash
        """
        try:
            hash_obj = hashlib.new(algorithm)

            with open(path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)

            return hash_obj.hexdigest()

        except Exception as e:
            self.logger.warning(f"Failed to calculate hash for {path}: {e}")
            return ""
        
    def organize_files(
        self,
        folder_path: str,
        organize_by: str = "type",
        create_subfolders: bool = True
    ) -> Dict[str, List[str]]:
        """Organize files in a folder by type, date, or size.

        Args:
            folder_path: Path to folder to organize
            organize_by: Organization method ('type', 'date', 'size')
            create_subfolders: Whether to create subfolders and move files

        Returns:
            Dictionary mapping categories to file lists
        """
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                raise FileError(f"Invalid folder path: {folder_path}")

            self.logger.info(f"Organizing files in {folder} by {organize_by}")

            # Get all files in folder
            files = [f for f in folder.iterdir() if f.is_file()]

            if organize_by == "type":
                return self._organize_by_type(files, folder, create_subfolders)
            elif organize_by == "date":
                return self._organize_by_date(files, folder, create_subfolders)
            elif organize_by == "size":
                return self._organize_by_size(files, folder, create_subfolders)
            else:
                raise FileError(f"Unknown organization method: {organize_by}")

        except Exception as e:
            self.logger.error(f"File organization failed: {e}")
            raise FileError(f"Organization failed: {e}")

    def _organize_by_type(
        self,
        files: List[Path],
        base_folder: Path,
        create_subfolders: bool
    ) -> Dict[str, List[str]]:
        """Organize files by type.

        Args:
            files: List of file paths
            base_folder: Base folder path
            create_subfolders: Whether to create subfolders

        Returns:
            Dictionary mapping file types to file lists
        """
        organization = {
            "videos": [],
            "images": [],
            "audio": [],
            "documents": [],
            "archives": [],
            "other": []
        }

        for file_path in files:
            try:
                file_info = self._detect_file_type(file_path)

                # Determine category
                if file_info.get("is_video", False):
                    category = "videos"
                elif file_info.get("is_image", False):
                    category = "images"
                elif file_info.get("is_audio", False):
                    category = "audio"
                elif file_info.get("is_document", False):
                    category = "documents"
                elif file_info.get("is_archive", False):
                    category = "archives"
                else:
                    category = "other"

                organization[category].append(str(file_path))

                # Move file to subfolder if requested
                if create_subfolders and category != "other":
                    subfolder = base_folder / category
                    subfolder.mkdir(exist_ok=True)

                    new_path = subfolder / file_path.name
                    if not new_path.exists():
                        file_path.rename(new_path)
                        organization[category][-1] = str(new_path)

            except Exception as e:
                self.logger.warning(f"Failed to organize file {file_path}: {e}")
                organization["other"].append(str(file_path))

        return organization

    def _organize_by_date(
        self,
        files: List[Path],
        base_folder: Path,
        create_subfolders: bool
    ) -> Dict[str, List[str]]:
        """Organize files by modification date.

        Args:
            files: List of file paths
            base_folder: Base folder path
            create_subfolders: Whether to create subfolders

        Returns:
            Dictionary mapping date periods to file lists
        """
        organization = {}

        for file_path in files:
            try:
                mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                date_key = mod_time.strftime("%Y-%m")  # Year-Month format

                if date_key not in organization:
                    organization[date_key] = []

                organization[date_key].append(str(file_path))

                # Move file to subfolder if requested
                if create_subfolders:
                    subfolder = base_folder / date_key
                    subfolder.mkdir(exist_ok=True)

                    new_path = subfolder / file_path.name
                    if not new_path.exists():
                        file_path.rename(new_path)
                        organization[date_key][-1] = str(new_path)

            except Exception as e:
                self.logger.warning(f"Failed to organize file {file_path}: {e}")
                if "unknown" not in organization:
                    organization["unknown"] = []
                organization["unknown"].append(str(file_path))

        return organization

    def _organize_by_size(
        self,
        files: List[Path],
        base_folder: Path,
        create_subfolders: bool
    ) -> Dict[str, List[str]]:
        """Organize files by size.

        Args:
            files: List of file paths
            base_folder: Base folder path
            create_subfolders: Whether to create subfolders

        Returns:
            Dictionary mapping size categories to file lists
        """
        organization = {
            "small": [],      # < 10MB
            "medium": [],     # 10MB - 100MB
            "large": [],      # 100MB - 1GB
            "very_large": []  # > 1GB
        }

        for file_path in files:
            try:
                size_bytes = file_path.stat().st_size

                # Determine size category
                if size_bytes < 10 * 1024 * 1024:  # < 10MB
                    category = "small"
                elif size_bytes < 100 * 1024 * 1024:  # < 100MB
                    category = "medium"
                elif size_bytes < 1024 * 1024 * 1024:  # < 1GB
                    category = "large"
                else:
                    category = "very_large"

                organization[category].append(str(file_path))

                # Move file to subfolder if requested
                if create_subfolders:
                    subfolder = base_folder / category
                    subfolder.mkdir(exist_ok=True)

                    new_path = subfolder / file_path.name
                    if not new_path.exists():
                        file_path.rename(new_path)
                        organization[category][-1] = str(new_path)

            except Exception as e:
                self.logger.warning(f"Failed to organize file {file_path}: {e}")
                organization["small"].append(str(file_path))

        return organization

    def batch_rename(
        self,
        folder_path: str,
        pattern: str,
        file_filter: Optional[str] = None
    ) -> List[str]:
        """Batch rename files in a folder.

        Args:
            folder_path: Path to folder containing files
            pattern: Naming pattern (e.g., "video_{counter:03d}")
            file_filter: File extension filter (e.g., ".mp4")

        Returns:
            List of new file paths
        """
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                raise FileError(f"Invalid folder path: {folder_path}")

            # Get files to rename
            files = [f for f in folder.iterdir() if f.is_file()]

            # Apply filter if specified
            if file_filter:
                files = [f for f in files if f.suffix.lower() == file_filter.lower()]

            # Sort files for consistent ordering
            files.sort(key=lambda x: x.name.lower())

            renamed_files = []
            counter = 1

            for file_path in files:
                try:
                    # Generate new name
                    new_name = pattern.format(
                        counter=counter,
                        original_name=file_path.stem,
                        extension=file_path.suffix[1:] if file_path.suffix else '',
                        date=datetime.now().strftime("%Y%m%d"),
                        time=datetime.now().strftime("%H%M%S")
                    )

                    # Add extension if not included in pattern
                    if not new_name.endswith(file_path.suffix):
                        new_name += file_path.suffix

                    new_path = self.rename_file(str(file_path), new_name)
                    renamed_files.append(new_path)
                    counter += 1

                except Exception as e:
                    self.logger.warning(f"Failed to rename {file_path}: {e}")

            self.logger.info(f"Batch renamed {len(renamed_files)} files")
            return renamed_files

        except Exception as e:
            self.logger.error(f"Batch rename failed: {e}")
            raise FileError(f"Batch rename failed: {e}")

    def find_duplicates(self, folder_path: str) -> Dict[str, List[str]]:
        """Find duplicate files in a folder based on content hash.

        Args:
            folder_path: Path to folder to scan

        Returns:
            Dictionary mapping hash values to lists of duplicate file paths
        """
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                raise FileError(f"Invalid folder path: {folder_path}")

            self.logger.info(f"Scanning for duplicates in {folder}")

            hash_map = {}
            files = [f for f in folder.rglob("*") if f.is_file()]

            for file_path in files:
                try:
                    file_hash = self._calculate_file_hash(file_path)
                    if file_hash:
                        if file_hash not in hash_map:
                            hash_map[file_hash] = []
                        hash_map[file_hash].append(str(file_path))

                except Exception as e:
                    self.logger.warning(f"Failed to hash file {file_path}: {e}")

            # Return only hashes with multiple files (duplicates)
            duplicates = {h: files for h, files in hash_map.items() if len(files) > 1}

            self.logger.info(f"Found {len(duplicates)} sets of duplicate files")
            return duplicates

        except Exception as e:
            self.logger.error(f"Duplicate detection failed: {e}")
            raise FileError(f"Duplicate detection failed: {e}")
        
    def get_file_info(self, file_path: str) -> Dict[str, any]:
        """Get basic information about a file.

        Args:
            file_path: Path to file

        Returns:
            Dictionary with file information
        """
        try:
            path = Path(file_path)
            if not path.exists():
                raise FileError(f"File does not exist: {file_path}")

            stat = path.stat()
            return {
                "name": path.name,
                "size": stat.st_size,
                "size_formatted": self._format_file_size(stat.st_size),
                "modified": stat.st_mtime,
                "modified_formatted": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
                "extension": path.suffix,
                "is_file": path.is_file(),
                "is_directory": path.is_dir()
            }
        except Exception as e:
            raise FileError(f"Failed to get file info: {e}")

    def create_directory(self, dir_path: str) -> bool:
        """Create a directory if it doesn't exist.

        Args:
            dir_path: Directory path to create

        Returns:
            True if created or already exists
        """
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directory: {dir_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to create directory {dir_path}: {e}")
            return False

    def get_folder_stats(self, folder_path: str) -> Dict[str, any]:
        """Get statistics about a folder.

        Args:
            folder_path: Path to folder

        Returns:
            Dictionary with folder statistics
        """
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                raise FileError(f"Invalid folder path: {folder_path}")

            stats = {
                "total_files": 0,
                "total_size": 0,
                "file_types": {},
                "largest_file": None,
                "oldest_file": None,
                "newest_file": None
            }

            oldest_time = float('inf')
            newest_time = 0
            largest_size = 0

            for file_path in folder.rglob("*"):
                if file_path.is_file():
                    try:
                        stat = file_path.stat()
                        stats["total_files"] += 1
                        stats["total_size"] += stat.st_size

                        # Track file types
                        ext = file_path.suffix.lower()
                        if ext not in stats["file_types"]:
                            stats["file_types"][ext] = 0
                        stats["file_types"][ext] += 1

                        # Track largest file
                        if stat.st_size > largest_size:
                            largest_size = stat.st_size
                            stats["largest_file"] = {
                                "path": str(file_path),
                                "size": stat.st_size,
                                "size_formatted": self._format_file_size(stat.st_size)
                            }

                        # Track oldest file
                        if stat.st_mtime < oldest_time:
                            oldest_time = stat.st_mtime
                            stats["oldest_file"] = {
                                "path": str(file_path),
                                "modified": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                            }

                        # Track newest file
                        if stat.st_mtime > newest_time:
                            newest_time = stat.st_mtime
                            stats["newest_file"] = {
                                "path": str(file_path),
                                "modified": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                            }

                    except Exception as e:
                        self.logger.warning(f"Failed to process file {file_path}: {e}")

            stats["total_size_formatted"] = self._format_file_size(stats["total_size"])
            return stats

        except Exception as e:
            self.logger.error(f"Failed to get folder stats: {e}")
            raise FileError(f"Folder stats failed: {e}")

    def cleanup_empty_folders(self, folder_path: str) -> int:
        """Remove empty folders recursively.

        Args:
            folder_path: Path to folder to clean up

        Returns:
            Number of folders removed
        """
        try:
            folder = Path(folder_path)
            if not folder.exists() or not folder.is_dir():
                raise FileError(f"Invalid folder path: {folder_path}")

            removed_count = 0

            # Process folders from deepest to shallowest
            for subfolder in sorted(folder.rglob("*"), key=lambda x: len(x.parts), reverse=True):
                if subfolder.is_dir():
                    try:
                        # Check if folder is empty
                        if not any(subfolder.iterdir()):
                            subfolder.rmdir()
                            removed_count += 1
                            self.logger.info(f"Removed empty folder: {subfolder}")
                    except Exception as e:
                        self.logger.warning(f"Failed to remove folder {subfolder}: {e}")

            return removed_count

        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
            raise FileError(f"Cleanup failed: {e}")

    def get_disk_usage(self, path: str) -> Dict[str, any]:
        """Get disk usage information for a path.

        Args:
            path: Path to check

        Returns:
            Dictionary with disk usage information
        """
        try:
            import shutil

            total, used, free = shutil.disk_usage(path)

            return {
                "total": total,
                "used": used,
                "free": free,
                "total_formatted": self._format_file_size(total),
                "used_formatted": self._format_file_size(used),
                "free_formatted": self._format_file_size(free),
                "usage_percent": (used / total) * 100 if total > 0 else 0
            }

        except Exception as e:
            self.logger.error(f"Failed to get disk usage: {e}")
            raise FileError(f"Disk usage check failed: {e}")
